# BakryAfand - نظام إدارة نقاط البيع العربي

نظام شامل لإدارة نقاط البيع مصمم خصيصاً للمتاجر العربية، يوفر واجهة سهلة الاستخدام باللغة العربية مع دعم كامل للاتجاه من اليمين إلى اليسار (RTL).

## 🌟 المميزات الرئيسية

### 📊 لوحة المعلومات
- عرض الإحصائيات الرئيسية في الوقت الفعلي
- مخططات بيانية للمبيعات والأرباح
- تنبيهات المخزون المنخفض
- ملخص المبيعات اليومية والشهرية

### 🛍️ إدارة المبيعات
- واجهة بيع سريعة وسهلة الاستخدام
- دعم طرق الدفع المختلفة (نقداً/آجل)
- طباعة الفواتير تلقائياً
- إدارة العملاء والخصومات
- حساب الضرائب تلقائياً

### 📦 إدارة المخزون
- إضافة وتعديل المنتجات
- تتبع الكميات والأسعار
- تصنيف المنتجات حسب الفئات
- تنبيهات المخزون المنخفض
- دعم الباركود

### 👥 إدارة العملاء
- قاعدة بيانات شاملة للعملاء
- تتبع تاريخ المشتريات
- إدارة الديون والمدفوعات
- كشوف حساب مفصلة
- إيصالات الاستلام

### 🚚 إدارة الموردين والمشتريات
- قاعدة بيانات الموردين
- تسجيل المشتريات وفواتير الموردين
- تحديث المخزون تلقائياً
- تتبع تكاليف البضائع

### 💰 إدارة الديون والمدفوعات
- تتبع الديون المستحقة
- تسجيل المدفوعات
- إيصالات الاستلام
- تذكيرات الدفع
- كشوف حساب العملاء

### 📈 التقارير والإحصائيات
- تقارير المبيعات التفصيلية
- تقارير المخزون والمنتجات
- تقارير العملاء والديون
- تقارير الأرباح والخسائر
- تقارير المشتريات والموردين
- إمكانية التصدير والطباعة

### ⚙️ الإعدادات والتخصيص
- إعدادات الشركة والمعلومات
- إدارة كلمات المرور
- النسخ الاحتياطي والاستعادة
- تخصيص الضرائب والعملة
- إعدادات التنبيهات

## 🚀 التقنيات المستخدمة

- **HTML5** - هيكل الصفحات
- **CSS3** - التصميم والأنماط مع دعم RTL
- **JavaScript (ES6+)** - المنطق والتفاعل
- **LocalStorage** - تخزين البيانات محلياً
- **Font Awesome** - الأيقونات
- **Google Fonts (Cairo)** - الخط العربي

## 📋 متطلبات التشغيل

- متصفح ويب حديث (Chrome, Firefox, Safari, Edge)
- لا يتطلب خادم أو قاعدة بيانات خارجية
- يعمل بالكامل في المتصفح

## 🔧 التثبيت والتشغيل

1. **تحميل الملفات:**
   ```bash
   git clone https://github.com/yourusername/bakryafand-pos.git
   cd bakryafand-pos
   ```

2. **تشغيل النظام:**
   - افتح ملف `index.html` في المتصفح مباشرة
   - أو استخدم خادم محلي:
   ```bash
   # باستخدام Python
   python -m http.server 8000
   
   # باستخدام Node.js
   npx http-server
   ```

3. **تسجيل الدخول:**
   - كلمة المرور الافتراضية: `123`
   - يمكن تغييرها من الإعدادات

## 📱 التصميم المتجاوب

النظام مصمم ليعمل على جميع الأجهزة:
- 💻 أجهزة الكمبيوتر المكتبية
- 💻 أجهزة الكمبيوتر المحمولة
- 📱 الهواتف الذكية
- 📱 الأجهزة اللوحية

## 🔒 الأمان

- تشفير كلمات المرور
- حماية البيانات محلياً
- نسخ احتياطية آمنة
- تسجيل دخول محمي

## 📊 البيانات التجريبية

النظام يأتي مع بيانات تجريبية تشمل:
- منتجات متنوعة (أجهزة كمبيوتر، ملحقات، هواتف)
- عملاء وموردين
- إعدادات افتراضية مناسبة للسوق العماني

## 🛠️ التخصيص

يمكن تخصيص النظام بسهولة:
- تغيير ألوان الواجهة
- إضافة شعار الشركة
- تعديل العملة والضرائب
- إضافة حقول مخصصة

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. تطبيق التغييرات
4. إرسال Pull Request

## 📞 الدعم والتواصل

- 📧 البريد الإلكتروني: <EMAIL>
- 🐛 الإبلاغ عن الأخطاء: [GitHub Issues](https://github.com/yourusername/bakryafand-pos/issues)
- 💬 المناقشات: [GitHub Discussions](https://github.com/yourusername/bakryafand-pos/discussions)

## 🔄 التحديثات المستقبلية

- [ ] دعم قواعد البيانات السحابية
- [ ] تطبيق الهاتف المحمول
- [ ] تكامل مع أنظمة الدفع الإلكتروني
- [ ] تقارير متقدمة مع مخططات تفاعلية
- [ ] دعم متعدد المستخدمين
- [ ] API للتكامل مع أنظمة أخرى

## 📸 لقطات الشاشة

### لوحة المعلومات
![لوحة المعلومات](screenshots/dashboard.png)

### واجهة المبيعات
![واجهة المبيعات](screenshots/sales.png)

### إدارة المنتجات
![إدارة المنتجات](screenshots/products.png)

---

**BakryAfand POS** - نظام إدارة نقاط البيع العربي الشامل 🇴🇲

صُنع بـ ❤️ للمتاجر العربية
