directories:
  output: dist
  buildResources: build
appId: com.bakryafand.pos
productName: BakryAfand POS
files:
  - filter:
      - '**/*'
      - '!node_modules'
      - '!dist'
      - '!.git'
      - '!README.md'
      - '!package-lock.json'
win:
  target:
    - target: nsis
      arch:
        - x64
        - ia32
    - target: portable
      arch:
        - x64
        - ia32
  requestedExecutionLevel: asInvoker
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: BakryAfand POS
  deleteAppDataOnUninstall: false
  runAfterFinish: true
  menuCategory: Business
  artifactName: BakryAfand-POS-Setup-${version}.${ext}
  displayLanguageSelector: false
  language: '1025'
portable:
  artifactName: ${productName}-${version}-portable.${ext}
mac:
  target: dmg
  category: public.app-category.business
linux:
  target: AppImage
  category: Office
extraResources:
  - from: assets/
    to: assets/
    filter:
      - '**/*'
electronVersion: 27.3.11
