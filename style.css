/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* الألوان الأساسية */
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --success-color: #4CAF50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --info-color: #2196F3;
    
    /* ألوان الخلفية */
    --bg-primary: #f0f2f5;
    --bg-secondary: #ffffff;
    --bg-dark: #2c3e50;
    
    /* ألوان النص */
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --text-light: #ffffff;
    
    /* الظلال */
    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 20px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 30px rgba(0,0,0,0.2);
    
    /* Neumorphism */
    --neu-shadow: 8px 8px 16px #d1d9e6, -8px -8px 16px #ffffff;
    --neu-inset: inset 8px 8px 16px #d1d9e6, inset -8px -8px 16px #ffffff;
    
    /* المسافات */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    
    /* الحدود */
    --border-radius: 12px;
    --border-radius-lg: 20px;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, var(--bg-primary) 0%, #e3f2fd 100%);
    color: var(--text-primary);
    line-height: 1.6;
    direction: rtl;
    min-height: 100vh;
}

/* شاشة تسجيل الدخول */
.login-screen {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    padding: var(--spacing-md);
}

.login-container {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--neu-shadow);
    padding: var(--spacing-xl);
    width: 100%;
    max-width: 400px;
    text-align: center;
}

.login-header .logo {
    margin-bottom: var(--spacing-lg);
}

.login-header .logo i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.login-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.login-header p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.login-form {
    margin-top: var(--spacing-lg);
}

.form-group {
    margin-bottom: var(--spacing-md);
    text-align: right;
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--text-primary);
}

.input-group {
    position: relative;
}

.input-group input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    padding-right: 3rem;
    border: 2px solid #e0e0e0;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: inherit;
    background: var(--bg-secondary);
    box-shadow: var(--neu-inset);
    transition: all 0.3s ease;
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-group i {
    position: absolute;
    right: var(--spacing-sm);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.login-btn {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--text-light);
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-medium);
    margin-bottom: var(--spacing-md);
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
}

.login-btn i {
    margin-left: var(--spacing-xs);
}

.login-info {
    background: #f8f9fa;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius);
    border-right: 4px solid var(--info-color);
}

.login-info p {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin: 0;
}

/* الواجهة الرئيسية */
.main-app {
    display: grid;
    grid-template-areas: 
        "sidebar header"
        "sidebar main";
    grid-template-columns: 280px 1fr;
    grid-template-rows: 70px 1fr;
    min-height: 100vh;
}

/* الشريط العلوي */
.top-header {
    grid-area: header;
    background: var(--bg-secondary);
    box-shadow: var(--shadow-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 var(--spacing-md);
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.sidebar-toggle {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--text-primary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.sidebar-toggle:hover {
    background: var(--bg-primary);
}

#pageTitle {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.logout-btn {
    background: var(--danger-color);
    color: var(--text-light);
    border: none;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    background: #d32f2f;
    transform: translateY(-1px);
}

/* القائمة الجانبية */
.sidebar {
    grid-area: sidebar;
    background: var(--bg-secondary);
    box-shadow: var(--shadow-medium);
    overflow-y: auto;
    z-index: 200;
}

.sidebar-header {
    padding: var(--spacing-md);
    border-bottom: 1px solid #e0e0e0;
}

.sidebar-header .logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.sidebar-header .logo i {
    font-size: 1.5rem;
}

.sidebar-nav ul {
    list-style: none;
    padding: var(--spacing-sm) 0;
}

.sidebar-nav .nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    text-decoration: none;
    transition: all 0.3s ease;
    border-right: 3px solid transparent;
}

.sidebar-nav .nav-link:hover {
    background: var(--bg-primary);
    border-right-color: var(--primary-color);
}

.sidebar-nav .nav-link.active {
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.1) 0%, transparent 100%);
    border-right-color: var(--primary-color);
    color: var(--primary-color);
    font-weight: 600;
}

.sidebar-nav .nav-link i {
    width: 20px;
    text-align: center;
}

/* المحتوى الرئيسي */
.main-content {
    grid-area: main;
    padding: var(--spacing-md);
    overflow-y: auto;
    background: var(--bg-primary);
}

.page-content {
    max-width: 1200px;
    margin: 0 auto;
}

/* النوافذ المنبثقة */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-heavy);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-md);
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    border-top: 1px solid #e0e0e0;
}

/* الأزرار */
.btn {
    padding: var(--spacing-xs) var(--spacing-md);
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.btn-primary {
    background: var(--primary-color);
    color: var(--text-light);
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--text-secondary);
    color: var(--text-light);
}

.btn-secondary:hover {
    background: #6c757d;
}

.btn-success {
    background: var(--success-color);
    color: var(--text-light);
}

.btn-success:hover {
    background: #45a049;
}

.btn-danger {
    background: var(--danger-color);
    color: var(--text-light);
}

.btn-danger:hover {
    background: #d32f2f;
}

.btn-warning {
    background: var(--warning-color);
    color: var(--text-light);
}

.btn-warning:hover {
    background: #f57c00;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .main-app {
        grid-template-areas: 
            "header"
            "main";
        grid-template-columns: 1fr;
        grid-template-rows: 70px 1fr;
    }
    
    .sidebar {
        position: fixed;
        top: 70px;
        right: -280px;
        height: calc(100vh - 70px);
        width: 280px;
        transition: right 0.3s ease;
    }
    
    .sidebar.show {
        right: 0;
    }
    
    .login-container {
        margin: var(--spacing-sm);
        padding: var(--spacing-lg);
    }
}

/* أنماط إضافية للمكونات */
.card {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    box-shadow: var(--neu-shadow);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid #e0e0e0;
}

.card-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.table {
    width: 100%;
    border-collapse: collapse;
    margin-top: var(--spacing-sm);
}

.table th,
.table td {
    padding: var(--spacing-sm);
    text-align: right;
    border-bottom: 1px solid #e0e0e0;
}

.table th {
    background: var(--bg-primary);
    font-weight: 600;
    color: var(--text-primary);
}

.table tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
}

/* أنماط النماذج */
.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-sm);
    border: 2px solid #e0e0e0;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: inherit;
    background: var(--bg-secondary);
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* أنماط الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.stat-card {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    box-shadow: var(--neu-shadow);
    padding: var(--spacing-md);
    text-align: center;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-sm);
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* أنماط التنبيهات */
.alert {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-md);
    border-right: 4px solid;
}

.alert-success {
    background: rgba(76, 175, 80, 0.1);
    border-right-color: var(--success-color);
    color: #2e7d32;
}

.alert-warning {
    background: rgba(255, 152, 0, 0.1);
    border-right-color: var(--warning-color);
    color: #ef6c00;
}

.alert-danger {
    background: rgba(244, 67, 54, 0.1);
    border-right-color: var(--danger-color);
    color: #c62828;
}

.alert-info {
    background: rgba(33, 150, 243, 0.1);
    border-right-color: var(--info-color);
    color: #1565c0;
}

/* أنماط إضافية للمكونات */
.badge {
    display: inline-block;
    padding: 0.25em 0.6em;
    font-size: 0.75em;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.375rem;
}

.badge-primary {
    color: var(--text-light);
    background-color: var(--primary-color);
}

.badge-secondary {
    color: var(--text-light);
    background-color: var(--text-secondary);
}

.badge-success {
    color: var(--text-light);
    background-color: var(--success-color);
}

.badge-danger {
    color: var(--text-light);
    background-color: var(--danger-color);
}

.badge-warning {
    color: var(--text-light);
    background-color: var(--warning-color);
}

.badge-info {
    color: var(--text-light);
    background-color: var(--info-color);
}

/* أنماط البحث */
.search-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.search-input {
    flex: 1;
    padding: var(--spacing-sm);
    border: 2px solid #e0e0e0;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: inherit;
    background: var(--bg-secondary);
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* أنماط الفلترة */
.filter-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    flex-wrap: wrap;
}

.filter-select {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 2px solid #e0e0e0;
    border-radius: var(--border-radius);
    background: var(--bg-secondary);
    font-family: inherit;
    transition: all 0.3s ease;
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* أنماط التحميل */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--spacing-lg);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* أنماط الصفحات الفارغة */
.empty-state {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: var(--spacing-md);
    color: #e0e0e0;
}

.empty-state h3 {
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

/* أنماط الإجراءات المجمعة */
.bulk-actions {
    display: none;
    background: var(--bg-secondary);
    border: 1px solid #e0e0e0;
    border-radius: var(--border-radius);
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    align-items: center;
    gap: var(--spacing-sm);
}

.bulk-actions.show {
    display: flex;
}

.bulk-actions-text {
    flex: 1;
    font-weight: 500;
}

/* أنماط التبديل */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: var(--primary-color);
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

/* أنماط التقييم بالنجوم */
.rating {
    display: flex;
    gap: 2px;
}

.rating .star {
    color: #ddd;
    font-size: 1.2rem;
    cursor: pointer;
    transition: color 0.2s ease;
}

.rating .star.active,
.rating .star:hover {
    color: #ffc107;
}

/* أنماط التقدم */
.progress {
    width: 100%;
    height: 20px;
    background-color: #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transition: width 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
    font-weight: 500;
}

/* أنماط الخطوات */
.steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    position: relative;
}

.step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 20px;
    right: -50%;
    width: 100%;
    height: 2px;
    background: #e0e0e0;
    z-index: 1;
}

.step.completed:not(:last-child)::after {
    background: var(--success-color);
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e0e0e0;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    position: relative;
    z-index: 2;
}

.step.active .step-number {
    background: var(--primary-color);
    color: white;
}

.step.completed .step-number {
    background: var(--success-color);
    color: white;
}

.step-title {
    font-size: 0.9rem;
    text-align: center;
    color: var(--text-secondary);
}

.step.active .step-title,
.step.completed .step-title {
    color: var(--text-primary);
    font-weight: 500;
}

/* أنماط الأكورديون */
.accordion {
    border: 1px solid #e0e0e0;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.accordion-item {
    border-bottom: 1px solid #e0e0e0;
}

.accordion-item:last-child {
    border-bottom: none;
}

.accordion-header {
    background: var(--bg-primary);
    padding: var(--spacing-md);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background 0.3s ease;
}

.accordion-header:hover {
    background: #e8f0fe;
}

.accordion-header.active {
    background: var(--primary-color);
    color: white;
}

.accordion-content {
    padding: var(--spacing-md);
    display: none;
}

.accordion-content.show {
    display: block;
}

/* أنماط التبويبات */
.tabs {
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: var(--spacing-md);
}

.tab-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.tab-item {
    margin-left: var(--spacing-sm);
}

.tab-link {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    text-decoration: none;
    color: var(--text-secondary);
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.tab-link:hover {
    color: var(--primary-color);
}

.tab-link.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* أنماط الإشعارات المنبثقة */
.toast-container {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 9999;
    max-width: 350px;
}

.toast {
    background: var(--bg-secondary);
    border: 1px solid #e0e0e0;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    margin-bottom: var(--spacing-sm);
    padding: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.toast.show {
    transform: translateX(0);
}

.toast-icon {
    font-size: 1.2rem;
}

.toast-success .toast-icon {
    color: var(--success-color);
}

.toast-error .toast-icon {
    color: var(--danger-color);
}

.toast-warning .toast-icon {
    color: var(--warning-color);
}

.toast-info .toast-icon {
    color: var(--info-color);
}

.toast-content {
    flex: 1;
}

.toast-title {
    font-weight: 600;
    margin-bottom: 2px;
}

.toast-message {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 2px;
}

/* تحسينات للطباعة */
@media print {
    .sidebar,
    .top-header,
    .btn,
    .modal,
    .toast-container {
        display: none !important;
    }

    .main-content {
        grid-area: unset;
        padding: 0;
        background: white;
    }

    .main-app {
        display: block;
    }

    body {
        background: white;
        color: black;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ddd;
        page-break-inside: avoid;
    }

    .table {
        font-size: 0.8rem;
    }

    .table th,
    .table td {
        padding: 0.3rem;
    }
}
