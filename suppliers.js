// إدارة الموردين
function loadSuppliers() {
    const pageContent = document.getElementById('pageContent');
    
    // إنشاء شريط الأدوات
    const toolbar = createSuppliersToolbar();
    pageContent.appendChild(toolbar);
    
    // إنشاء جدول الموردين
    const suppliersTable = createSuppliersTable();
    pageContent.appendChild(suppliersTable);
}

// إنشاء شريط أدوات الموردين
function createSuppliersToolbar() {
    const toolbar = document.createElement('div');
    toolbar.className = 'card';
    toolbar.style.marginBottom = '1.5rem';
    
    const toolbarContent = document.createElement('div');
    toolbarContent.style.display = 'flex';
    toolbarContent.style.justifyContent = 'space-between';
    toolbarContent.style.alignItems = 'center';
    toolbarContent.style.flexWrap = 'wrap';
    toolbarContent.style.gap = '1rem';
    
    // الجانب الأيسر - البحث
    const leftSide = document.createElement('div');
    leftSide.style.display = 'flex';
    leftSide.style.alignItems = 'center';
    leftSide.style.gap = '1rem';
    leftSide.style.flex = '1';
    
    // حقل البحث
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.placeholder = 'البحث في الموردين...';
    searchInput.className = 'search-input';
    searchInput.style.maxWidth = '300px';
    searchInput.id = 'supplierSearch';
    
    leftSide.appendChild(searchInput);
    
    // الجانب الأيمن - الأزرار
    const rightSide = document.createElement('div');
    rightSide.style.display = 'flex';
    rightSide.style.alignItems = 'center';
    rightSide.style.gap = '0.5rem';
    
    // زر إضافة مورد جديد
    const addButton = document.createElement('button');
    addButton.className = 'btn btn-primary';
    addButton.innerHTML = '<i class="fas fa-plus"></i> إضافة مورد';
    addButton.addEventListener('click', () => showSupplierModal());
    
    // زر تصدير
    const exportButton = document.createElement('button');
    exportButton.className = 'btn btn-success';
    exportButton.innerHTML = '<i class="fas fa-download"></i> تصدير';
    exportButton.addEventListener('click', exportSuppliers);
    
    // زر طباعة
    const printButton = document.createElement('button');
    printButton.className = 'btn btn-info';
    printButton.innerHTML = '<i class="fas fa-print"></i> طباعة';
    printButton.addEventListener('click', printSuppliers);
    
    rightSide.appendChild(addButton);
    rightSide.appendChild(exportButton);
    rightSide.appendChild(printButton);
    
    toolbarContent.appendChild(leftSide);
    toolbarContent.appendChild(rightSide);
    toolbar.appendChild(toolbarContent);
    
    // إضافة مستمع الحدث للبحث
    searchInput.addEventListener('input', filterSuppliers);
    
    return toolbar;
}

// إنشاء جدول الموردين
function createSuppliersTable() {
    const suppliers = db.getSuppliers();
    
    const tableContainer = document.createElement('div');
    tableContainer.className = 'card';
    
    if (suppliers.length === 0) {
        tableContainer.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-truck"></i>
                <h3>لا توجد موردين</h3>
                <p>ابدأ بإضافة موردين جدد لمتجرك</p>
                <button class="btn btn-primary" onclick="showSupplierModal()">
                    <i class="fas fa-plus"></i> إضافة أول مورد
                </button>
            </div>
        `;
        return tableContainer;
    }
    
    const table = document.createElement('table');
    table.className = 'table';
    table.id = 'suppliersTable';
    
    // رأس الجدول
    table.innerHTML = `
        <thead>
            <tr>
                <th>اسم المورد</th>
                <th>الهاتف</th>
                <th>البريد الإلكتروني</th>
                <th>العنوان</th>
                <th>الرصيد</th>
                <th>آخر مشترى</th>
                <th>تاريخ التسجيل</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            ${suppliers.map(supplier => createSupplierRow(supplier)).join('')}
        </tbody>
    `;
    
    tableContainer.appendChild(table);
    return tableContainer;
}

// إنشاء صف مورد
function createSupplierRow(supplier) {
    const purchases = db.getPurchases().filter(p => p.supplierId === supplier.id);
    const lastPurchase = purchases.length > 0 ? purchases[purchases.length - 1] : null;
    
    const balanceClass = supplier.balance > 0 ? 'badge-success' : 
                        supplier.balance < 0 ? 'badge-danger' : 'badge-secondary';
    
    const balanceText = supplier.balance > 0 ? 'دائن' : 
                       supplier.balance < 0 ? 'مدين' : 'متوازن';
    
    return `
        <tr data-supplier-id="${supplier.id}">
            <td>
                <div>
                    <strong>${supplier.name}</strong>
                    <br><small style="color: #666;">ID: ${supplier.id.slice(-6)}</small>
                </div>
            </td>
            <td>${supplier.phone || '-'}</td>
            <td>${supplier.email || '-'}</td>
            <td>${supplier.address || '-'}</td>
            <td>
                <span class="badge ${balanceClass}">
                    ${db.formatCurrency(Math.abs(supplier.balance))} ${balanceText}
                </span>
            </td>
            <td>${lastPurchase ? db.formatDate(lastPurchase.createdAt) : 'لا توجد مشتريات'}</td>
            <td>${db.formatDate(supplier.createdAt)}</td>
            <td>
                <button class="btn btn-info" onclick="viewSupplier('${supplier.id}')" title="عرض">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-warning" onclick="editSupplier('${supplier.id}')" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-success" onclick="addPurchase('${supplier.id}')" title="مشترى">
                    <i class="fas fa-shopping-bag"></i>
                </button>
                <button class="btn btn-danger" onclick="deleteSupplier('${supplier.id}')" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `;
}

// فلترة الموردين
function filterSuppliers() {
    const searchTerm = document.getElementById('supplierSearch').value.toLowerCase();
    const rows = document.querySelectorAll('#suppliersTable tbody tr');
    
    rows.forEach(row => {
        const supplierId = row.dataset.supplierId;
        const supplier = db.getSupplierById(supplierId);
        
        const supplierName = supplier.name.toLowerCase();
        const supplierPhone = (supplier.phone || '').toLowerCase();
        const supplierEmail = (supplier.email || '').toLowerCase();
        
        const matchesSearch = supplierName.includes(searchTerm) || 
                             supplierPhone.includes(searchTerm) || 
                             supplierEmail.includes(searchTerm);
        
        row.style.display = matchesSearch ? '' : 'none';
    });
}

// عرض نافذة إضافة/تعديل مورد
function showSupplierModal(supplierId = null) {
    const isEdit = !!supplierId;
    const supplier = isEdit ? db.getSupplierById(supplierId) : {};
    
    const fields = [
        {
            name: 'name',
            label: 'اسم المورد',
            type: 'text',
            required: true,
            value: supplier.name || '',
            placeholder: 'أدخل اسم المورد'
        },
        {
            name: 'phone',
            label: 'رقم الهاتف',
            type: 'tel',
            value: supplier.phone || '',
            placeholder: 'رقم الهاتف'
        },
        {
            name: 'email',
            label: 'البريد الإلكتروني',
            type: 'email',
            value: supplier.email || '',
            placeholder: 'البريد الإلكتروني'
        },
        {
            name: 'address',
            label: 'العنوان',
            type: 'textarea',
            value: supplier.address || '',
            placeholder: 'عنوان المورد',
            rows: 3
        },
        {
            name: 'company',
            label: 'اسم الشركة',
            type: 'text',
            value: supplier.company || '',
            placeholder: 'اسم الشركة (اختياري)'
        },
        {
            name: 'taxNumber',
            label: 'الرقم الضريبي',
            type: 'text',
            value: supplier.taxNumber || '',
            placeholder: 'الرقم الضريبي (اختياري)'
        }
    ];
    
    const form = AppHelpers.createForm(fields, (data) => {
        saveSupplier(data, supplierId);
    }, isEdit ? 'تحديث' : 'إضافة');
    
    const actions = [
        {
            text: 'إلغاء',
            type: 'secondary',
            handler: () => {}
        }
    ];
    
    AppHelpers.createModal(
        isEdit ? 'تعديل المورد' : 'إضافة مورد جديد',
        form,
        actions
    );
}

// حفظ المورد
function saveSupplier(data, supplierId = null) {
    try {
        const supplierData = {
            name: data.name.trim(),
            phone: data.phone.trim(),
            email: data.email.trim(),
            address: data.address.trim(),
            company: data.company.trim(),
            taxNumber: data.taxNumber.trim()
        };
        
        if (supplierId) {
            supplierData.id = supplierId;
        }
        
        db.saveSupplier(supplierData);
        
        app.showAlert(
            supplierId ? 'تم تحديث المورد بنجاح' : 'تم إضافة المورد بنجاح',
            'success'
        );
        
        loadSuppliers(); // إعادة تحميل الصفحة
    } catch (error) {
        app.showAlert('حدث خطأ أثناء حفظ المورد', 'danger');
    }
}

// تعديل مورد
function editSupplier(supplierId) {
    showSupplierModal(supplierId);
}

// حذف مورد
function deleteSupplier(supplierId) {
    const supplier = db.getSupplierById(supplierId);
    if (!supplier) return;
    
    // التحقق من وجود مشتريات للمورد
    const purchases = db.getPurchases().filter(p => p.supplierId === supplierId);
    if (purchases.length > 0) {
        app.showAlert('لا يمكن حذف المورد لوجود مشتريات مرتبطة به', 'warning');
        return;
    }
    
    app.showConfirm(
        `هل أنت متأكد من حذف المورد "${supplier.name}"؟`,
        () => {
            db.deleteSupplier(supplierId);
            app.showAlert('تم حذف المورد بنجاح', 'success');
            loadSuppliers();
        },
        'تأكيد الحذف'
    );
}

// عرض تفاصيل المورد
function viewSupplier(supplierId) {
    const supplier = db.getSupplierById(supplierId);
    if (!supplier) return;
    
    const purchases = db.getPurchases().filter(p => p.supplierId === supplierId);
    const totalPurchases = purchases.reduce((sum, purchase) => sum + purchase.total, 0);
    
    const content = `
        <div class="supplier-details">
            <div class="supplier-info">
                <h4>معلومات المورد</h4>
                <div class="info-grid">
                    <div><strong>الاسم:</strong> ${supplier.name}</div>
                    <div><strong>الشركة:</strong> ${supplier.company || 'غير محدد'}</div>
                    <div><strong>الهاتف:</strong> ${supplier.phone || 'غير محدد'}</div>
                    <div><strong>البريد:</strong> ${supplier.email || 'غير محدد'}</div>
                    <div><strong>العنوان:</strong> ${supplier.address || 'غير محدد'}</div>
                    <div><strong>الرقم الضريبي:</strong> ${supplier.taxNumber || 'غير محدد'}</div>
                    <div><strong>تاريخ التسجيل:</strong> ${db.formatDate(supplier.createdAt)}</div>
                    <div><strong>الرصيد الحالي:</strong> 
                        <span class="badge ${supplier.balance > 0 ? 'badge-success' : supplier.balance < 0 ? 'badge-danger' : 'badge-secondary'}">
                            ${db.formatCurrency(Math.abs(supplier.balance))} ${supplier.balance > 0 ? 'دائن' : supplier.balance < 0 ? 'مدين' : 'متوازن'}
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="supplier-stats">
                <h4>إحصائيات المورد</h4>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-value">${db.toArabicNumbers(purchases.length)}</span>
                        <span class="stat-label">عدد المشتريات</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">${db.formatCurrency(totalPurchases)}</span>
                        <span class="stat-label">إجمالي المشتريات</span>
                    </div>
                </div>
            </div>
            
            <div class="recent-purchases">
                <h4>آخر المشتريات</h4>
                ${purchases.length > 0 ? `
                    <table class="table">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>المبلغ</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${purchases.slice(-5).reverse().map(purchase => `
                                <tr>
                                    <td>${db.toArabicNumbers(purchase.invoiceNumber || purchase.id.slice(-6))}</td>
                                    <td>${db.formatCurrency(purchase.total)}</td>
                                    <td>${db.formatDate(purchase.createdAt)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                ` : '<p>لا توجد مشتريات</p>'}
            </div>
        </div>
        
        <style>
            .supplier-details h4 {
                margin-bottom: 1rem;
                color: var(--primary-color);
                border-bottom: 2px solid var(--primary-color);
                padding-bottom: 0.5rem;
            }
            .info-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 1rem;
                margin-bottom: 2rem;
            }
            .stats-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
                margin-bottom: 2rem;
            }
            .stat-item {
                text-align: center;
                padding: 1rem;
                background: #f8f9fa;
                border-radius: 8px;
            }
            .stat-value {
                display: block;
                font-size: 1.5rem;
                font-weight: bold;
                color: var(--primary-color);
            }
            .stat-label {
                font-size: 0.9rem;
                color: #666;
            }
            .recent-purchases {
                margin-top: 2rem;
            }
        </style>
    `;
    
    const actions = [
        {
            text: 'تعديل',
            type: 'warning',
            icon: 'fas fa-edit',
            handler: () => editSupplier(supplierId),
            close: false
        },
        {
            text: 'إضافة مشترى',
            type: 'success',
            icon: 'fas fa-shopping-bag',
            handler: () => addPurchase(supplierId),
            close: false
        },
        {
            text: 'إغلاق',
            type: 'secondary',
            handler: () => {}
        }
    ];
    
    AppHelpers.createModal(`تفاصيل المورد: ${supplier.name}`, content, actions);
}

// إضافة مشترى (سيتم تطويرها في ملف purchases.js)
function addPurchase(supplierId) {
    app.showAlert('سيتم تطوير هذه الميزة قريباً', 'info');
}

// تصدير الموردين
function exportSuppliers() {
    const suppliers = db.getSuppliers();
    
    if (suppliers.length === 0) {
        app.showAlert('لا توجد موردين للتصدير', 'warning');
        return;
    }
    
    const csvData = [
        ['اسم المورد', 'الشركة', 'الهاتف', 'البريد الإلكتروني', 'العنوان', 'الرقم الضريبي', 'الرصيد', 'تاريخ التسجيل']
    ];
    
    suppliers.forEach(supplier => {
        csvData.push([
            supplier.name,
            supplier.company || '',
            supplier.phone || '',
            supplier.email || '',
            supplier.address || '',
            supplier.taxNumber || '',
            supplier.balance,
            db.formatDate(supplier.createdAt)
        ]);
    });
    
    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `suppliers_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        app.showAlert('تم تصدير الموردين بنجاح', 'success');
    }
}

// طباعة الموردين
function printSuppliers() {
    const suppliers = db.getSuppliers();
    
    if (suppliers.length === 0) {
        app.showAlert('لا توجد موردين للطباعة', 'warning');
        return;
    }
    
    const content = `
        <h2 style="text-align: center; margin-bottom: 30px;">قائمة الموردين</h2>
        <table style="width: 100%; border-collapse: collapse;">
            <thead>
                <tr style="background-color: #f5f5f5;">
                    <th style="border: 1px solid #ddd; padding: 8px;">اسم المورد</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">الشركة</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">الهاتف</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">البريد</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">الرصيد</th>
                </tr>
            </thead>
            <tbody>
                ${suppliers.map(supplier => `
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px;">${supplier.name}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${supplier.company || '-'}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${supplier.phone || '-'}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${supplier.email || '-'}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${db.formatCurrency(Math.abs(supplier.balance))} ${supplier.balance > 0 ? 'دائن' : supplier.balance < 0 ? 'مدين' : 'متوازن'}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
        <div style="margin-top: 30px; text-align: center;">
            <p><strong>إجمالي الموردين:</strong> ${db.toArabicNumbers(suppliers.length)} مورد</p>
        </div>
    `;
    
    app.printContent(content, 'قائمة الموردين');
}
