; تخصيص مثبت BakryAfand POS
; هذا الملف يحتوي على تخصيصات إضافية للمثبت

; إضافة رسالة ترحيب
!macro customWelcomePage
  !insertmacro MUI_PAGE_WELCOME
!macroend

; إضافة صفحة الترخيص
!macro customLicensePage
  !insertmacro MUI_PAGE_LICENSE "LICENSE"
!macroend

; تخصيص صفحة اختيار المجلد
!macro customDirectoryPage
  !insertmacro MUI_PAGE_DIRECTORY
!macroend

; تخصيص صفحة التثبيت
!macro customInstallPage
  !insertmacro MUI_PAGE_INSTFILES
!macroend

; تخصيص صفحة الانتهاء
!macro customFinishPage
  !insertmacro MUI_PAGE_FINISH
!macroend

; إضافة ملفات إضافية للتثبيت
!macro customInstallMode
  ; إنشاء مجلد البيانات
  CreateDirectory "$PROFILE\BakryAfand-POS-Data"
  
  ; إنشاء اختصار في قائمة ابدأ
  CreateDirectory "$SMPROGRAMS\BakryAfand POS"
  CreateShortCut "$SMPROGRAMS\BakryAfand POS\BakryAfand POS.lnk" "$INSTDIR\BakryAfand POS.exe"
  CreateShortCut "$SMPROGRAMS\BakryAfand POS\إلغاء التثبيت.lnk" "$INSTDIR\Uninstall BakryAfand POS.exe"
  
  ; إنشاء اختصار على سطح المكتب
  CreateShortCut "$DESKTOP\BakryAfand POS.lnk" "$INSTDIR\BakryAfand POS.exe"
  
  ; تسجيل التطبيق في النظام
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\BakryAfand POS" "DisplayName" "BakryAfand POS"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\BakryAfand POS" "UninstallString" "$INSTDIR\Uninstall BakryAfand POS.exe"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\BakryAfand POS" "DisplayIcon" "$INSTDIR\BakryAfand POS.exe"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\BakryAfand POS" "Publisher" "BakryAfand"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\BakryAfand POS" "DisplayVersion" "1.0.0"
!macroend

; تخصيص عملية إلغاء التثبيت
!macro customUnInstallMode
  ; حذف الاختصارات
  Delete "$DESKTOP\BakryAfand POS.lnk"
  Delete "$SMPROGRAMS\BakryAfand POS\BakryAfand POS.lnk"
  Delete "$SMPROGRAMS\BakryAfand POS\إلغاء التثبيت.lnk"
  RMDir "$SMPROGRAMS\BakryAfand POS"
  
  ; حذف مفاتيح التسجيل
  DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\BakryAfand POS"
  
  ; سؤال المستخدم عن حذف البيانات
  MessageBox MB_YESNO|MB_ICONQUESTION "هل تريد حذف جميع بيانات التطبيق؟$\n$\nملاحظة: سيتم حذف جميع البيانات المحفوظة نهائياً." IDYES delete_data IDNO keep_data
  
  delete_data:
    RMDir /r "$PROFILE\BakryAfand-POS-Data"
    MessageBox MB_OK "تم حذف جميع البيانات بنجاح."
    Goto done
  
  keep_data:
    MessageBox MB_OK "تم الاحتفاظ بالبيانات في:$\n$PROFILE\BakryAfand-POS-Data"
  
  done:
!macroend

; رسائل مخصصة
LangString WELCOME_TITLE ${LANG_ARABIC} "مرحباً بك في مثبت BakryAfand POS"
LangString WELCOME_TEXT ${LANG_ARABIC} "هذا المعالج سيقوم بتثبيت نظام نقاط البيع BakryAfand على جهازك.$\n$\nيُنصح بإغلاق جميع التطبيقات الأخرى قبل المتابعة."

LangString DIR_TEXT ${LANG_ARABIC} "اختر المجلد الذي تريد تثبيت BakryAfand POS فيه:"

LangString FINISH_TITLE ${LANG_ARABIC} "تم إكمال التثبيت"
LangString FINISH_TEXT ${LANG_ARABIC} "تم تثبيت BakryAfand POS بنجاح على جهازك.$\n$\nيمكنك الآن تشغيل التطبيق من قائمة ابدأ أو من اختصار سطح المكتب."

; إعدادات إضافية
!define MUI_WELCOMEPAGE_TITLE $(WELCOME_TITLE)
!define MUI_WELCOMEPAGE_TEXT $(WELCOME_TEXT)
!define MUI_DIRECTORYPAGE_TEXT_TOP $(DIR_TEXT)
!define MUI_FINISHPAGE_TITLE $(FINISH_TITLE)
!define MUI_FINISHPAGE_TEXT $(FINISH_TEXT)

; تفعيل تشغيل التطبيق بعد التثبيت
!define MUI_FINISHPAGE_RUN "$INSTDIR\BakryAfand POS.exe"
!define MUI_FINISHPAGE_RUN_TEXT "تشغيل BakryAfand POS الآن"

; إعدادات المثبت
RequestExecutionLevel admin
ShowInstDetails show
ShowUnInstDetails show

; معلومات الإصدار
VIProductVersion "*******"
VIAddVersionKey "ProductName" "BakryAfand POS"
VIAddVersionKey "CompanyName" "BakryAfand"
VIAddVersionKey "LegalCopyright" "© 2024 BakryAfand. جميع الحقوق محفوظة."
VIAddVersionKey "FileDescription" "نظام نقاط البيع BakryAfand"
VIAddVersionKey "FileVersion" "1.0.0"
VIAddVersionKey "ProductVersion" "1.0.0"
VIAddVersionKey "InternalName" "BakryAfand POS"
VIAddVersionKey "OriginalFilename" "BakryAfand-POS-Setup.exe"
