// اختبار بسيط لنظام المشتريات
async function testPurchasesSystem() {
    console.log('🧪 بدء اختبار نظام المشتريات...');
    
    const results = {
        functions: {},
        data: {},
        operations: {}
    };
    
    // 1. اختبار وجود الدوال
    console.log('📋 اختبار وجود الدوال...');
    const functions = [
        'showPurchaseModal',
        'addPurchaseItem', 
        'handleAddPurchaseItem',
        'savePurchase',
        'addPurchase',
        'loadPurchases'
    ];
    
    functions.forEach(funcName => {
        const exists = typeof window[funcName] === 'function';
        results.functions[funcName] = exists;
        console.log(`${exists ? '✅' : '❌'} ${funcName}: ${exists ? 'موجودة' : 'غير موجودة'}`);
    });
    
    // 2. اختبار البيانات
    console.log('📊 اختبار البيانات...');
    try {
        const suppliers = await db.getSuppliers();
        const products = await db.getProducts();
        const purchases = await db.getPurchases();
        
        results.data = {
            suppliers: suppliers.length,
            products: products.length,
            purchases: purchases.length
        };
        
        console.log(`📦 المنتجات: ${products.length}`);
        console.log(`🏪 الموردين: ${suppliers.length}`);
        console.log(`🛒 المشتريات: ${purchases.length}`);
        
        if (suppliers.length === 0) {
            console.log('⚠️ لا توجد موردين - إضافة مورد تجريبي...');
            await addTestSupplier();
        }
        
        if (products.length === 0) {
            console.log('⚠️ لا توجد منتجات - إضافة منتج تجريبي...');
            await addTestProduct();
        }
        
    } catch (error) {
        console.error('❌ خطأ في فحص البيانات:', error);
        results.data.error = error.message;
    }
    
    // 3. اختبار العمليات
    console.log('⚙️ اختبار العمليات...');
    
    // اختبار فتح نافذة المشتريات
    if (typeof showPurchaseModal === 'function') {
        try {
            console.log('🔄 اختبار فتح نافذة المشتريات...');
            await showPurchaseModal();
            results.operations.showModal = true;
            console.log('✅ تم فتح النافذة بنجاح');
        } catch (error) {
            console.error('❌ خطأ في فتح النافذة:', error);
            results.operations.showModal = false;
            results.operations.showModalError = error.message;
        }
    } else {
        console.log('❌ دالة showPurchaseModal غير موجودة');
        results.operations.showModal = false;
    }
    
    // عرض النتائج النهائية
    console.log('📋 ملخص النتائج:');
    console.table(results);
    
    // إنشاء تقرير مرئي
    createVisualReport(results);
    
    return results;
}

// إضافة مورد تجريبي
async function addTestSupplier() {
    try {
        const testSupplier = {
            name: 'مورد تجريبي للاختبار',
            phone: '12345678',
            email: '<EMAIL>',
            address: 'عنوان تجريبي',
            notes: 'مورد للاختبار فقط'
        };
        
        await db.saveSupplier(testSupplier);
        console.log('✅ تم إضافة المورد التجريبي');
        return true;
    } catch (error) {
        console.error('❌ خطأ في إضافة المورد:', error);
        return false;
    }
}

// إضافة منتج تجريبي
async function addTestProduct() {
    try {
        const testProduct = {
            name: 'منتج تجريبي للمشتريات',
            description: 'منتج للاختبار',
            category: 'اختبار',
            price: 25.50,
            quantity: 0,
            barcode: 'TEST123'
        };
        
        await db.saveProduct(testProduct);
        console.log('✅ تم إضافة المنتج التجريبي');
        return true;
    } catch (error) {
        console.error('❌ خطأ في إضافة المنتج:', error);
        return false;
    }
}

// إنشاء تقرير مرئي
function createVisualReport(results) {
    // إنشاء نافذة التقرير
    const reportWindow = document.createElement('div');
    reportWindow.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        border: 2px solid #007bff;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        z-index: 10000;
        max-width: 500px;
        max-height: 80vh;
        overflow-y: auto;
        font-family: Arial, sans-serif;
    `;
    
    let html = `
        <h3 style="color: #007bff; margin-top: 0;">🧪 تقرير اختبار نظام المشتريات</h3>
        
        <div style="margin: 15px 0;">
            <h4>📋 الدوال:</h4>
            <ul style="list-style: none; padding: 0;">
    `;
    
    Object.entries(results.functions).forEach(([func, exists]) => {
        html += `<li style="margin: 5px 0;">${exists ? '✅' : '❌'} ${func}</li>`;
    });
    
    html += `
            </ul>
        </div>
        
        <div style="margin: 15px 0;">
            <h4>📊 البيانات:</h4>
            <ul style="list-style: none; padding: 0;">
                <li>📦 المنتجات: ${results.data.products || 0}</li>
                <li>🏪 الموردين: ${results.data.suppliers || 0}</li>
                <li>🛒 المشتريات: ${results.data.purchases || 0}</li>
            </ul>
        </div>
        
        <div style="margin: 15px 0;">
            <h4>⚙️ العمليات:</h4>
            <ul style="list-style: none; padding: 0;">
                <li>${results.operations.showModal ? '✅' : '❌'} فتح نافذة المشتريات</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 20px;">
            <button onclick="this.parentElement.parentElement.remove()" 
                    style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                إغلاق
            </button>
        </div>
    `;
    
    reportWindow.innerHTML = html;
    document.body.appendChild(reportWindow);
    
    // إغلاق تلقائي بعد 10 ثوان
    setTimeout(() => {
        if (reportWindow.parentElement) {
            reportWindow.remove();
        }
    }, 10000);
}

// اختبار سريع للزر
function testPurchaseButton() {
    console.log('🔘 اختبار زر إضافة المشتري...');
    
    // البحث عن الزر في الصفحة
    const buttons = document.querySelectorAll('button');
    let purchaseButton = null;
    
    buttons.forEach(button => {
        if (button.textContent.includes('إضافة مشترى') || button.textContent.includes('إضافة مشتري')) {
            purchaseButton = button;
        }
    });
    
    if (purchaseButton) {
        console.log('✅ تم العثور على زر إضافة المشتري');
        console.log('🔄 محاولة الضغط على الزر...');
        
        try {
            purchaseButton.click();
            console.log('✅ تم الضغط على الزر بنجاح');
        } catch (error) {
            console.error('❌ خطأ في الضغط على الزر:', error);
        }
    } else {
        console.log('❌ لم يتم العثور على زر إضافة المشتري');
        console.log('🔍 الأزرار الموجودة:');
        buttons.forEach((button, index) => {
            console.log(`${index + 1}. ${button.textContent.trim()}`);
        });
    }
}

// تشغيل الاختبار عند تحميل الصفحة
if (typeof window !== 'undefined') {
    // إضافة الدوال للنطاق العام
    window.testPurchasesSystem = testPurchasesSystem;
    window.testPurchaseButton = testPurchaseButton;
    window.addTestSupplier = addTestSupplier;
    window.addTestProduct = addTestProduct;
    
    console.log('🧪 أدوات اختبار المشتريات جاهزة!');
    console.log('📝 استخدم الأوامر التالية:');
    console.log('   testPurchasesSystem() - اختبار شامل');
    console.log('   testPurchaseButton() - اختبار الزر');
}
