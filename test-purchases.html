<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام المشتريات - BakryAfand POS</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #218838;
        }
        .data-count {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        .count-item {
            background: white;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
            text-align: center;
        }
        .count-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار نظام المشتريات</h1>
        <p>هذه الصفحة لاختبار جميع وظائف نظام المشتريات في BakryAfand POS</p>
        
        <div class="test-section">
            <h3>حالة البيانات الحالية</h3>
            <button onclick="checkCurrentData()">فحص البيانات</button>
            <div id="current-data-status"></div>
        </div>
        
        <div class="test-section">
            <h3>اختبار دوال المشتريات</h3>
            <button onclick="testPurchaseFunctions()">اختبار الدوال</button>
            <div id="functions-test-results"></div>
        </div>
        
        <div class="test-section">
            <h3>اختبار إضافة مورد تجريبي</h3>
            <button onclick="addTestSupplier()">إضافة مورد تجريبي</button>
            <div id="supplier-test-results"></div>
        </div>
        
        <div class="test-section">
            <h3>اختبار إضافة منتج تجريبي</h3>
            <button onclick="addTestProduct()">إضافة منتج تجريبي</button>
            <div id="product-test-results"></div>
        </div>
        
        <div class="test-section">
            <h3>اختبار فتح نافذة المشتريات</h3>
            <button class="btn-success" onclick="testShowPurchaseModal()">اختبار فتح النافذة</button>
            <div id="modal-test-results"></div>
        </div>
        
        <div class="test-section">
            <h3>اختبار إضافة مشترى كامل</h3>
            <button class="btn-success" onclick="testCompletePurchase()">اختبار مشترى كامل</button>
            <div id="complete-purchase-results"></div>
        </div>
        
        <div class="test-section">
            <h3>العودة للنظام</h3>
            <button onclick="window.location.href='index.html'">العودة للنظام الرئيسي</button>
        </div>
    </div>

    <script src="database.js"></script>
    <script src="app.js"></script>
    <script>
        // إنشاء كائن app مؤقت للاختبار
        if (typeof app === 'undefined') {
            window.app = {
                showAlert: function(message, type = 'info') {
                    console.log(`[${type.toUpperCase()}] ${message}`);
                    alert(`${type.toUpperCase()}: ${message}`);
                },
                showConfirm: function(message, callback) {
                    if (confirm(message)) {
                        callback();
                    }
                }
            };
        }

        // إنشاء كائن AppHelpers مؤقت للاختبار
        if (typeof AppHelpers === 'undefined') {
            window.AppHelpers = {
                createModal: function(title, content, actions) {
                    console.log('Modal created:', title);
                    return { element: document.createElement('div') };
                }
            };
        }
    </script>
    <script src="purchases.js"></script>
    <script>
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            container.appendChild(div);
        }
        
        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }
        
        async function checkCurrentData() {
            clearResults('current-data-status');
            addResult('current-data-status', 'فحص البيانات الحالية...', 'info');
            
            try {
                const products = await db.getProducts();
                const suppliers = await db.getSuppliers();
                const purchases = await db.getPurchases();
                
                const container = document.getElementById('current-data-status');
                const countsDiv = document.createElement('div');
                countsDiv.className = 'data-count';
                countsDiv.innerHTML = `
                    <div class="count-item">
                        <div class="count-number">${products.length}</div>
                        <div>منتجات</div>
                    </div>
                    <div class="count-item">
                        <div class="count-number">${suppliers.length}</div>
                        <div>موردين</div>
                    </div>
                    <div class="count-item">
                        <div class="count-number">${purchases.length}</div>
                        <div>مشتريات</div>
                    </div>
                `;
                container.appendChild(countsDiv);
                
                addResult('current-data-status', '✓ تم فحص البيانات بنجاح', 'success');
                
                if (products.length === 0) {
                    addResult('current-data-status', '⚠️ لا توجد منتجات - قد تحتاج لإضافة منتجات أولاً', 'warning');
                }
                
                if (suppliers.length === 0) {
                    addResult('current-data-status', '⚠️ لا توجد موردين - قد تحتاج لإضافة موردين أولاً', 'warning');
                }
                
            } catch (error) {
                addResult('current-data-status', `✗ خطأ في فحص البيانات: ${error.message}`, 'error');
            }
        }
        
        function testPurchaseFunctions() {
            clearResults('functions-test-results');
            addResult('functions-test-results', 'اختبار وجود دوال المشتريات...', 'info');

            // انتظار تحميل الملفات
            setTimeout(() => {
                const functions = [
                    'showPurchaseModal',
                    'addPurchaseItem',
                    'handleAddPurchaseItem',
                    'savePurchase',
                    'addPurchase'
                ];

                functions.forEach(funcName => {
                    if (typeof window[funcName] === 'function') {
                        addResult('functions-test-results', `✓ دالة ${funcName} موجودة`, 'success');
                    } else {
                        addResult('functions-test-results', `✗ دالة ${funcName} غير موجودة`, 'error');

                        // محاولة البحث في النطاقات الأخرى
                        try {
                            if (eval(`typeof ${funcName}`) === 'function') {
                                addResult('functions-test-results', `✓ دالة ${funcName} موجودة في نطاق آخر`, 'warning');
                            }
                        } catch (e) {
                            // تجاهل الأخطاء
                        }
                    }
                });

                // اختبار إضافي للتأكد من تحميل purchases.js
                if (typeof loadPurchases === 'function') {
                    addResult('functions-test-results', '✓ ملف purchases.js محمل بنجاح', 'success');
                } else {
                    addResult('functions-test-results', '⚠️ قد يكون هناك مشكلة في تحميل purchases.js', 'warning');
                }
            }, 500);
        }
        
        async function addTestSupplier() {
            clearResults('supplier-test-results');
            addResult('supplier-test-results', 'إضافة مورد تجريبي...', 'info');
            
            try {
                const testSupplier = {
                    name: 'مورد تجريبي للاختبار',
                    phone: '12345678',
                    email: '<EMAIL>',
                    address: 'عنوان تجريبي',
                    notes: 'مورد للاختبار فقط'
                };
                
                await db.saveSupplier(testSupplier);
                addResult('supplier-test-results', '✓ تم إضافة المورد التجريبي بنجاح', 'success');
                
            } catch (error) {
                addResult('supplier-test-results', `✗ خطأ في إضافة المورد: ${error.message}`, 'error');
            }
        }
        
        async function addTestProduct() {
            clearResults('product-test-results');
            addResult('product-test-results', 'إضافة منتج تجريبي...', 'info');
            
            try {
                const testProduct = {
                    name: 'منتج تجريبي للمشتريات',
                    description: 'منتج للاختبار',
                    category: 'اختبار',
                    price: 25.50,
                    quantity: 0, // نبدأ بكمية صفر لاختبار المشتريات
                    barcode: 'TEST123'
                };
                
                await db.saveProduct(testProduct);
                addResult('product-test-results', '✓ تم إضافة المنتج التجريبي بنجاح', 'success');
                
            } catch (error) {
                addResult('product-test-results', `✗ خطأ في إضافة المنتج: ${error.message}`, 'error');
            }
        }
        
        async function testShowPurchaseModal() {
            clearResults('modal-test-results');
            addResult('modal-test-results', 'اختبار فتح نافذة المشتريات...', 'info');

            try {
                // التحقق من وجود البيانات المطلوبة
                const suppliers = await db.getSuppliers();
                const products = await db.getProducts();

                if (suppliers.length === 0) {
                    addResult('modal-test-results', '⚠️ لا توجد موردين - سيتم إضافة مورد تجريبي', 'warning');
                    await addTestSupplier();
                }

                if (products.length === 0) {
                    addResult('modal-test-results', '⚠️ لا توجد منتجات - سيتم إضافة منتج تجريبي', 'warning');
                    await addTestProduct();
                }

                // اختبار فتح النافذة
                if (typeof showPurchaseModal === 'function') {
                    addResult('modal-test-results', '✓ دالة showPurchaseModal موجودة', 'success');
                    addResult('modal-test-results', 'محاولة فتح النافذة...', 'info');

                    await showPurchaseModal();
                    addResult('modal-test-results', '✓ تم فتح نافذة المشتريات بنجاح', 'success');
                } else {
                    addResult('modal-test-results', '✗ دالة showPurchaseModal غير موجودة', 'error');

                    // محاولة استدعاء مباشر
                    addResult('modal-test-results', 'محاولة استدعاء مباشر...', 'info');
                    try {
                        // محاولة تحميل الصفحة الرئيسية للوصول للدوال
                        window.location.href = 'index.html#purchases';
                    } catch (directError) {
                        addResult('modal-test-results', `✗ فشل الاستدعاء المباشر: ${directError.message}`, 'error');
                    }
                }

            } catch (error) {
                addResult('modal-test-results', `✗ خطأ في فتح النافذة: ${error.message}`, 'error');
                console.error('خطأ تفصيلي:', error);
            }
        }
        
        async function testCompletePurchase() {
            clearResults('complete-purchase-results');
            addResult('complete-purchase-results', 'اختبار إضافة مشترى كامل...', 'info');
            
            try {
                // التأكد من وجود البيانات
                let suppliers = await db.getSuppliers();
                let products = await db.getProducts();
                
                if (suppliers.length === 0) {
                    await addTestSupplier();
                    suppliers = await db.getSuppliers();
                }
                
                if (products.length === 0) {
                    await addTestProduct();
                    products = await db.getProducts();
                }
                
                // إنشاء مشترى تجريبي
                const testPurchase = {
                    supplierId: suppliers[0].id,
                    invoiceNumber: 'TEST-001',
                    date: new Date().toISOString().split('T')[0],
                    items: [
                        {
                            productId: products[0].id,
                            quantity: 10,
                            price: 20.00
                        }
                    ],
                    notes: 'مشترى تجريبي للاختبار'
                };
                
                // حساب المجموع
                testPurchase.total = testPurchase.items.reduce((sum, item) => sum + (item.quantity * item.price), 0);
                
                // حفظ المشترى
                await db.savePurchase(testPurchase);
                
                addResult('complete-purchase-results', '✓ تم إضافة المشترى التجريبي بنجاح', 'success');
                addResult('complete-purchase-results', `المورد: ${suppliers[0].name}`, 'info');
                addResult('complete-purchase-results', `المنتج: ${products[0].name}`, 'info');
                addResult('complete-purchase-results', `الكمية: 10`, 'info');
                addResult('complete-purchase-results', `السعر: 20.00`, 'info');
                addResult('complete-purchase-results', `المجموع: ${testPurchase.total.toFixed(2)}`, 'info');
                
                // التحقق من تحديث المخزون
                const updatedProduct = await db.getProductById(products[0].id);
                addResult('complete-purchase-results', `كمية المنتج بعد المشترى: ${updatedProduct.quantity}`, 'info');
                
            } catch (error) {
                addResult('complete-purchase-results', `✗ خطأ في إضافة المشترى: ${error.message}`, 'error');
                console.error('خطأ تفصيلي:', error);
            }
        }
        
        // فحص البيانات عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(() => {
                if (typeof db !== 'undefined') {
                    checkCurrentData();
                    testPurchaseFunctions();
                } else {
                    addResult('current-data-status', 'انتظار تهيئة قاعدة البيانات...', 'info');
                }
            }, 1000);
        });
    </script>
</body>
</html>
