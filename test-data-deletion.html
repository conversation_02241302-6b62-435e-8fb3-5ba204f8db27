<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حذف البيانات - نظا<PERSON> BakryAfand POS</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background: #e0a800;
        }
        .data-count {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        .count-item {
            background: white;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
            text-align: center;
        }
        .count-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار وظائف حذف البيانات</h1>
        <p>هذه الصفحة لاختبار وظائف حذف البيانات في نظام BakryAfand POS</p>
        
        <div class="test-section">
            <h3>حالة البيانات الحالية</h3>
            <button onclick="checkCurrentData()">فحص البيانات الحالية</button>
            <div id="current-data-status"></div>
        </div>
        
        <div class="test-section">
            <h3>اختبار حذف جميع البيانات</h3>
            <p><strong>تحذير:</strong> يحذف جميع البيانات نهائياً بما في ذلك البيانات الافتراضية</p>
            <button class="btn-danger" onclick="testDataDeletion()">اختبار حذف جميع البيانات</button>
            <div id="delete-test-results"></div>
        </div>

        <div class="test-section">
            <h3>اختبار إعادة تهيئة النظام</h3>
            <p>إعادة إنشاء البيانات الافتراضية (يستخدم بعد الحذف النهائي)</p>
            <button class="btn-warning" onclick="testResetToDefaults()">اختبار إعادة التهيئة</button>
            <div id="reset-defaults-results"></div>
        </div>
        
        <div class="test-section">
            <h3>إضافة بيانات تجريبية</h3>
            <p>لاختبار وظائف الحذف</p>
            <button onclick="addTestData()">إضافة بيانات تجريبية</button>
            <div id="test-data-results"></div>
        </div>
        
        <div class="test-section">
            <h3>العودة للنظام</h3>
            <button onclick="window.location.href='index.html'">العودة للنظام الرئيسي</button>
        </div>
    </div>

    <script src="database.js"></script>
    <script>
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            container.appendChild(div);
        }
        
        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }
        
        async function checkCurrentData() {
            clearResults('current-data-status');
            addResult('current-data-status', 'فحص البيانات الحالية...', 'info');
            
            try {
                const products = await db.getProducts();
                const customers = await db.getCustomers();
                const suppliers = await db.getSuppliers();
                const sales = await db.getSales();
                const purchases = await db.getPurchases();
                const payments = await db.getPayments();
                
                const container = document.getElementById('current-data-status');
                const countsDiv = document.createElement('div');
                countsDiv.className = 'data-count';
                countsDiv.innerHTML = `
                    <div class="count-item">
                        <div class="count-number">${products.length}</div>
                        <div>منتجات</div>
                    </div>
                    <div class="count-item">
                        <div class="count-number">${customers.length}</div>
                        <div>عملاء</div>
                    </div>
                    <div class="count-item">
                        <div class="count-number">${suppliers.length}</div>
                        <div>موردين</div>
                    </div>
                    <div class="count-item">
                        <div class="count-number">${sales.length}</div>
                        <div>مبيعات</div>
                    </div>
                    <div class="count-item">
                        <div class="count-number">${purchases.length}</div>
                        <div>مشتريات</div>
                    </div>
                    <div class="count-item">
                        <div class="count-number">${payments.length}</div>
                        <div>مدفوعات</div>
                    </div>
                `;
                container.appendChild(countsDiv);
                
                addResult('current-data-status', '✓ تم فحص البيانات بنجاح', 'success');
                
            } catch (error) {
                addResult('current-data-status', `✗ خطأ في فحص البيانات: ${error.message}`, 'error');
            }
        }
        
        async function testDataDeletion() {
            clearResults('delete-test-results');
            addResult('delete-test-results', 'اختبار حذف جميع البيانات...', 'warning');

            try {
                // فحص البيانات قبل الحذف
                const beforeProducts = await db.getProducts();
                const beforeCustomers = await db.getCustomers();
                const beforeSales = await db.getSales();
                const beforePurchases = await db.getPurchases();

                addResult('delete-test-results', `البيانات قبل الحذف: ${beforeProducts.length} منتج، ${beforeCustomers.length} عميل، ${beforeSales.length} مبيعة، ${beforePurchases.length} مشترى`, 'info');

                // تنفيذ الحذف النهائي
                const success = await db.clearAllData();

                if (success) {
                    addResult('delete-test-results', '✓ تم تنفيذ حذف جميع البيانات بنجاح', 'success');

                    // فحص البيانات بعد الحذف
                    try {
                        const afterProducts = await db.getProducts();
                        const afterCustomers = await db.getCustomers();
                        const afterSales = await db.getSales();
                        const afterPurchases = await db.getPurchases();

                        addResult('delete-test-results', `البيانات بعد الحذف: ${afterProducts.length} منتج، ${afterCustomers.length} عميل، ${afterSales.length} مبيعة، ${afterPurchases.length} مشترى`, 'info');

                        if (afterProducts.length === 0 && afterCustomers.length === 0 && afterSales.length === 0 && afterPurchases.length === 0) {
                            addResult('delete-test-results', '✓ تم حذف جميع البيانات نهائياً (لا توجد بيانات افتراضية)', 'success');
                        } else {
                            addResult('delete-test-results', '⚠️ لا زالت هناك بيانات موجودة بعد الحذف', 'warning');
                        }
                    } catch (dataError) {
                        addResult('delete-test-results', '✓ لا يمكن الوصول للبيانات - تم الحذف النهائي بنجاح', 'success');
                    }
                } else {
                    addResult('delete-test-results', '✗ فشل في حذف البيانات', 'error');
                }

            } catch (error) {
                addResult('delete-test-results', `✗ خطأ في اختبار حذف البيانات: ${error.message}`, 'error');
            }
        }
        
        async function addTestData() {
            clearResults('test-data-results');
            addResult('test-data-results', 'إضافة بيانات تجريبية...', 'info');
            
            try {
                // إضافة منتج تجريبي
                const testProduct = {
                    name: 'منتج تجريبي',
                    category: 'اختبار',
                    price: 10,
                    quantity: 100,
                    description: 'منتج للاختبار'
                };
                
                await db.saveProduct(testProduct);
                
                // إضافة عميل تجريبي
                const testCustomer = {
                    name: 'عميل تجريبي',
                    phone: '12345678',
                    email: '<EMAIL>',
                    address: 'عنوان تجريبي',
                    balance: 0
                };
                
                await db.saveCustomer(testCustomer);
                
                addResult('test-data-results', '✓ تم إضافة البيانات التجريبية بنجاح', 'success');
                
            } catch (error) {
                addResult('test-data-results', `✗ خطأ في إضافة البيانات التجريبية: ${error.message}`, 'error');
            }
        }
        
        // فحص البيانات عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(() => {
                if (typeof db !== 'undefined') {
                    checkCurrentData();
                } else {
                    addResult('current-data-status', 'انتظار تهيئة قاعدة البيانات...', 'info');
                    window.addEventListener('databaseReady', checkCurrentData);
                }
            }, 1000);
        });
    </script>
</body>
</html>
