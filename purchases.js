// نظام المشتريات
async function loadPurchases() {
    const pageContent = document.getElementById('pageContent');

    // إنشاء شريط الأدوات
    const toolbar = await createPurchasesToolbar();
    pageContent.appendChild(toolbar);

    // إنشاء جدول المشتريات
    const purchasesTable = await createPurchasesTable();
    pageContent.appendChild(purchasesTable);
}

// إنشاء شريط أدوات المشتريات
async function createPurchasesToolbar() {
    const toolbar = document.createElement('div');
    toolbar.className = 'card';
    toolbar.style.marginBottom = '1.5rem';

    const toolbarContent = document.createElement('div');
    toolbarContent.style.display = 'flex';
    toolbarContent.style.justifyContent = 'space-between';
    toolbarContent.style.alignItems = 'center';
    toolbarContent.style.flexWrap = 'wrap';
    toolbarContent.style.gap = '1rem';

    // الجانب الأيسر - البحث والفلترة
    const leftSide = document.createElement('div');
    leftSide.style.display = 'flex';
    leftSide.style.alignItems = 'center';
    leftSide.style.gap = '1rem';
    leftSide.style.flex = '1';

    // حقل البحث
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.placeholder = 'البحث في المشتريات...';
    searchInput.className = 'search-input';
    searchInput.style.maxWidth = '300px';
    searchInput.id = 'purchaseSearch';

    // فلتر الموردين
    const supplierFilter = document.createElement('select');
    supplierFilter.className = 'filter-select';
    supplierFilter.id = 'supplierFilter';

    const suppliers = await db.getSuppliers();
    supplierFilter.innerHTML = `
        <option value="">جميع الموردين</option>
        ${suppliers.map(supplier => `<option value="${supplier.id}">${supplier.name}</option>`).join('')}
    `;

    // فلتر التاريخ
    const dateFilter = document.createElement('select');
    dateFilter.className = 'filter-select';
    dateFilter.id = 'dateFilter';
    dateFilter.innerHTML = `
        <option value="">جميع الفترات</option>
        <option value="today">اليوم</option>
        <option value="week">هذا الأسبوع</option>
        <option value="month">هذا الشهر</option>
        <option value="year">هذا العام</option>
    `;

    leftSide.appendChild(searchInput);
    leftSide.appendChild(supplierFilter);
    leftSide.appendChild(dateFilter);

    // الجانب الأيمن - الأزرار
    const rightSide = document.createElement('div');
    rightSide.style.display = 'flex';
    rightSide.style.alignItems = 'center';
    rightSide.style.gap = '0.5rem';

    // زر إضافة مشترى جديد
    const addButton = document.createElement('button');
    addButton.className = 'btn btn-primary';
    addButton.innerHTML = '<i class="fas fa-plus"></i> إضافة مشترى';
    addButton.addEventListener('click', async () => {
        try {
            await showPurchaseModal();
        } catch (error) {
            console.error('خطأ في فتح نافذة المشترى:', error);
            app.showAlert('حدث خطأ أثناء فتح نافذة المشترى', 'danger');
        }
    });

    // زر تصدير
    const exportButton = document.createElement('button');
    exportButton.className = 'btn btn-success';
    exportButton.innerHTML = '<i class="fas fa-download"></i> تصدير';
    exportButton.addEventListener('click', exportPurchases);

    // زر طباعة
    const printButton = document.createElement('button');
    printButton.className = 'btn btn-info';
    printButton.innerHTML = '<i class="fas fa-print"></i> طباعة';
    printButton.addEventListener('click', printPurchases);

    rightSide.appendChild(addButton);
    rightSide.appendChild(exportButton);
    rightSide.appendChild(printButton);

    toolbarContent.appendChild(leftSide);
    toolbarContent.appendChild(rightSide);
    toolbar.appendChild(toolbarContent);

    // إضافة مستمعي الأحداث للبحث والفلترة
    searchInput.addEventListener('input', filterPurchases);
    supplierFilter.addEventListener('change', filterPurchases);
    dateFilter.addEventListener('change', filterPurchases);

    return toolbar;
}

// إنشاء جدول المشتريات
async function createPurchasesTable() {
    const purchases = await db.getPurchases();

    const tableContainer = document.createElement('div');
    tableContainer.className = 'card';

    if (purchases.length === 0) {
        tableContainer.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-shopping-bag"></i>
                <h3>لا توجد مشتريات</h3>
                <p>ابدأ بتسجيل مشترياتك من الموردين</p>
                <button class="btn btn-primary" onclick="showPurchaseModal()">
                    <i class="fas fa-plus"></i> إضافة أول مشترى
                </button>
            </div>
        `;
        return tableContainer;
    }

    const table = document.createElement('table');
    table.className = 'table';
    table.id = 'purchasesTable';

    // رأس الجدول
    table.innerHTML = `
        <thead>
            <tr>
                <th>رقم الفاتورة</th>
                <th>المورد</th>
                <th>عدد الأصناف</th>
                <th>المبلغ الإجمالي</th>
                <th>طريقة الدفع</th>
                <th>التاريخ</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            ${purchases.map(purchase => createPurchaseRow(purchase)).join('')}
        </tbody>
    `;

    tableContainer.appendChild(table);
    return tableContainer;
}

// إنشاء صف مشترى
function createPurchaseRow(purchase) {
    const supplier = db.getSupplierById(purchase.supplierId);

    return `
        <tr data-purchase-id="${purchase.id}">
            <td>
                <strong>${db.toArabicNumbers(purchase.invoiceNumber || purchase.id.slice(-6))}</strong>
            </td>
            <td>${supplier ? supplier.name : 'مورد محذوف'}</td>
            <td>${db.toArabicNumbers(purchase.items.length)}</td>
            <td>${db.formatCurrency(purchase.total)}</td>
            <td>
                <span class="badge ${purchase.paymentMethod === 'cash' ? 'badge-success' : 'badge-warning'}">
                    ${purchase.paymentMethod === 'cash' ? 'نقداً' : 'آجل'}
                </span>
            </td>
            <td>${db.formatDate(purchase.createdAt)}</td>
            <td>
                <button class="btn btn-info" onclick="viewPurchase('${purchase.id}')" title="عرض">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-success" onclick="printPurchaseInvoice('${purchase.id}')" title="طباعة">
                    <i class="fas fa-print"></i>
                </button>
                <button class="btn btn-danger" onclick="deletePurchase('${purchase.id}')" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `;
}

// فلترة المشتريات
function filterPurchases() {
    const searchTerm = document.getElementById('purchaseSearch').value.toLowerCase();
    const selectedSupplier = document.getElementById('supplierFilter').value;
    const selectedDate = document.getElementById('dateFilter').value;
    const rows = document.querySelectorAll('#purchasesTable tbody tr');

    rows.forEach(row => {
        const purchaseId = row.dataset.purchaseId;
        const purchase = db.getPurchases().find(p => p.id === purchaseId);
        const supplier = db.getSupplierById(purchase.supplierId);

        const matchesSearch = (purchase.invoiceNumber || purchase.id).toLowerCase().includes(searchTerm) ||
                             (supplier && supplier.name.toLowerCase().includes(searchTerm));

        const matchesSupplier = !selectedSupplier || purchase.supplierId === selectedSupplier;

        let matchesDate = true;
        if (selectedDate) {
            const purchaseDate = new Date(purchase.createdAt);
            const now = new Date();

            switch (selectedDate) {
                case 'today':
                    matchesDate = purchaseDate.toDateString() === now.toDateString();
                    break;
                case 'week':
                    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    matchesDate = purchaseDate >= weekAgo;
                    break;
                case 'month':
                    matchesDate = purchaseDate.getMonth() === now.getMonth() &&
                                 purchaseDate.getFullYear() === now.getFullYear();
                    break;
                case 'year':
                    matchesDate = purchaseDate.getFullYear() === now.getFullYear();
                    break;
            }
        }

        row.style.display = matchesSearch && matchesSupplier && matchesDate ? '' : 'none';
    });
}

// عرض نافذة إضافة مشترى
async function showPurchaseModal() {
    try {
        const suppliers = await db.getSuppliers();
        const products = await db.getProducts();

        if (suppliers.length === 0) {
            app.showAlert('يجب إضافة موردين أولاً', 'warning');
            return;
        }

        if (products.length === 0) {
            app.showAlert('يجب إضافة منتجات أولاً', 'warning');
            return;
        }

    const content = `
        <div class="purchase-form">
            <div class="form-row">
                <div class="form-group">
                    <label>المورد</label>
                    <select id="purchaseSupplier" required>
                        <option value="">اختر المورد</option>
                        ${suppliers.map(supplier => `<option value="${supplier.id}">${supplier.name}</option>`).join('')}
                    </select>
                </div>
                <div class="form-group">
                    <label>رقم الفاتورة</label>
                    <input type="text" id="purchaseInvoiceNumber" placeholder="رقم فاتورة المورد">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label>طريقة الدفع</label>
                    <select id="purchasePaymentMethod">
                        <option value="cash">نقداً</option>
                        <option value="credit">آجل</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>تاريخ الفاتورة</label>
                    <input type="date" id="purchaseDate" value="${new Date().toISOString().split('T')[0]}">
                </div>
            </div>

            <div class="purchase-items">
                <h4>أصناف المشترى</h4>
                <div class="items-header">
                    <button type="button" class="btn btn-success" onclick="handleAddPurchaseItem()">
                        <i class="fas fa-plus"></i> إضافة صنف
                    </button>
                </div>
                <div id="purchaseItemsList" class="items-list">
                    <!-- سيتم إضافة الأصناف هنا -->
                </div>
            </div>

            <div class="purchase-summary">
                <div class="summary-row">
                    <span>المجموع الفرعي:</span>
                    <span id="purchaseSubtotal">٠.٠٠ ر.ع</span>
                </div>
                <div class="summary-row">
                    <span>الضريبة (${db.getSettings().taxRate}%):</span>
                    <span id="purchaseTax">٠.٠٠ ر.ع</span>
                </div>
                <div class="summary-row total-row">
                    <span><strong>المجموع الكلي:</strong></span>
                    <span id="purchaseTotal"><strong>٠.٠٠ ر.ع</strong></span>
                </div>
            </div>

            <div class="form-group">
                <label>ملاحظات</label>
                <textarea id="purchaseNotes" placeholder="ملاحظات إضافية..." rows="3"></textarea>
            </div>
        </div>

        <style>
            .purchase-form .form-row {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 1rem;
                margin-bottom: 1rem;
            }
            .purchase-items {
                margin: 1.5rem 0;
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 1rem;
            }
            .items-header {
                margin-bottom: 1rem;
            }
            .items-list {
                max-height: 300px;
                overflow-y: auto;
            }
            .purchase-item {
                display: grid;
                grid-template-columns: 2fr 1fr 1fr 1fr auto;
                gap: 0.5rem;
                align-items: center;
                padding: 0.5rem;
                border: 1px solid #eee;
                border-radius: 4px;
                margin-bottom: 0.5rem;
            }
            .purchase-summary {
                border-top: 2px solid #ddd;
                padding-top: 1rem;
                margin-top: 1rem;
            }
            .summary-row {
                display: flex;
                justify-content: space-between;
                padding: 0.25rem 0;
            }
            .total-row {
                border-top: 1px solid #333;
                margin-top: 0.5rem;
                padding-top: 0.5rem;
                font-size: 1.1rem;
            }
        </style>
    `;

    const actions = [
        {
            text: 'حفظ المشترى',
            type: 'primary',
            icon: 'fas fa-save',
            handler: savePurchase,
            close: false
        },
        {
            text: 'إلغاء',
            type: 'secondary',
            handler: () => {}
        }
    ];

    const modal = AppHelpers.createModal('إضافة مشترى جديد', content, actions);

    // إضافة صنف افتراضي
    setTimeout(async () => {
        await addPurchaseItem();
    }, 100);

    } catch (error) {
        console.error('خطأ في فتح نافذة المشترى:', error);
        app.showAlert('حدث خطأ أثناء فتح نافذة المشترى', 'danger');
    }
}

// دالة wrapper للتعامل مع onclick
function handleAddPurchaseItem() {
    addPurchaseItem().catch(error => {
        console.error('خطأ في إضافة صنف:', error);
        app.showAlert('حدث خطأ أثناء إضافة الصنف', 'danger');
    });
}

// إضافة صنف للمشترى
async function addPurchaseItem() {
    const products = await db.getProducts();
    const itemsList = document.getElementById('purchaseItemsList');
    const itemIndex = itemsList.children.length;

    const itemDiv = document.createElement('div');
    itemDiv.className = 'purchase-item';
    itemDiv.innerHTML = `
        <select class="item-product" onchange="updatePurchaseItem(${itemIndex})">
            <option value="">اختر المنتج</option>
            ${products.map(product => `<option value="${product.id}">${product.name}</option>`).join('')}
        </select>
        <input type="number" class="item-quantity" placeholder="الكمية" min="1" onchange="updatePurchaseItem(${itemIndex})">
        <input type="number" class="item-price" placeholder="سعر الشراء" min="0" step="0.01" onchange="updatePurchaseItem(${itemIndex})">
        <span class="item-total">٠.٠٠</span>
        <button type="button" class="btn btn-danger" onclick="removePurchaseItem(${itemIndex})">
            <i class="fas fa-times"></i>
        </button>
    `;

    itemsList.appendChild(itemDiv);
}

// إزالة صنف من المشترى
function removePurchaseItem(index) {
    const itemsList = document.getElementById('purchaseItemsList');
    const items = itemsList.children;

    if (items.length > 1) {
        items[index].remove();
        updatePurchaseSummary();
    } else {
        app.showAlert('يجب أن يحتوي المشترى على صنف واحد على الأقل', 'warning');
    }
}

// تحديث صنف المشترى
function updatePurchaseItem(index) {
    const itemsList = document.getElementById('purchaseItemsList');
    const item = itemsList.children[index];

    const quantity = parseFloat(item.querySelector('.item-quantity').value) || 0;
    const price = parseFloat(item.querySelector('.item-price').value) || 0;
    const total = quantity * price;

    item.querySelector('.item-total').textContent = db.formatCurrency(total);

    updatePurchaseSummary();
}

// تحديث ملخص المشترى
function updatePurchaseSummary() {
    const itemsList = document.getElementById('purchaseItemsList');
    const items = Array.from(itemsList.children);

    let subtotal = 0;
    items.forEach(item => {
        const quantity = parseFloat(item.querySelector('.item-quantity').value) || 0;
        const price = parseFloat(item.querySelector('.item-price').value) || 0;
        subtotal += quantity * price;
    });

    const settings = db.getSettings();
    const tax = subtotal * (settings.taxRate / 100);
    const total = subtotal + tax;

    document.getElementById('purchaseSubtotal').textContent = db.formatCurrency(subtotal);
    document.getElementById('purchaseTax').textContent = db.formatCurrency(tax);
    document.getElementById('purchaseTotal').textContent = db.formatCurrency(total);
}

// حفظ المشترى
async function savePurchase() {
    const supplierId = document.getElementById('purchaseSupplier').value;
    const invoiceNumber = document.getElementById('purchaseInvoiceNumber').value.trim();
    const paymentMethod = document.getElementById('purchasePaymentMethod').value;
    const purchaseDate = document.getElementById('purchaseDate').value;
    const notes = document.getElementById('purchaseNotes').value.trim();

    if (!supplierId) {
        app.showAlert('يجب اختيار المورد', 'warning');
        return;
    }

    // جمع الأصناف
    const itemsList = document.getElementById('purchaseItemsList');
    const items = Array.from(itemsList.children);
    const purchaseItems = [];

    let hasError = false;
    items.forEach((item, index) => {
        const productId = item.querySelector('.item-product').value;
        const quantity = parseFloat(item.querySelector('.item-quantity').value) || 0;
        const price = parseFloat(item.querySelector('.item-price').value) || 0;

        if (!productId) {
            app.showAlert(`يجب اختيار المنتج في الصنف ${index + 1}`, 'warning');
            hasError = true;
            return;
        }

        if (quantity <= 0) {
            app.showAlert(`يجب إدخال كمية صحيحة في الصنف ${index + 1}`, 'warning');
            hasError = true;
            return;
        }

        if (price <= 0) {
            app.showAlert(`يجب إدخال سعر صحيح في الصنف ${index + 1}`, 'warning');
            hasError = true;
            return;
        }

        const product = db.getProductById(productId);
        purchaseItems.push({
            productId: productId,
            productName: product.name,
            quantity: quantity,
            price: price
        });
    });

    if (hasError || purchaseItems.length === 0) {
        return;
    }

    // حساب المجاميع
    const subtotal = purchaseItems.reduce((sum, item) => sum + (item.quantity * item.price), 0);
    const settings = db.getSettings();
    const tax = subtotal * (settings.taxRate / 100);
    const total = subtotal + tax;

    const purchaseData = {
        supplierId: supplierId,
        invoiceNumber: invoiceNumber || generatePurchaseInvoiceNumber(),
        items: purchaseItems,
        subtotal: subtotal,
        tax: tax,
        total: total,
        paymentMethod: paymentMethod,
        notes: notes,
        purchaseDate: purchaseDate
    };

    try {
        const savedPurchase = await db.savePurchase(purchaseData);

        app.showAlert('تم حفظ المشترى بنجاح', 'success');

        // إغلاق النافذة
        document.querySelectorAll('.modal').forEach(modal => {
            modal.classList.remove('show');
            setTimeout(() => modal.remove(), 300);
        });

        // إعادة تحميل الصفحة
        await loadPurchases();

        // طباعة سند الاستلام
        printPurchaseReceipt(savedPurchase);

    } catch (error) {
        app.showAlert('حدث خطأ أثناء حفظ المشترى', 'danger');
    }
}

// إنشاء رقم فاتورة مشترى
function generatePurchaseInvoiceNumber() {
    const now = new Date();
    const year = now.getFullYear().toString().slice(-2);
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const time = now.getTime().toString().slice(-4);

    return `P${year}${month}${day}${time}`;
}

// عرض تفاصيل المشترى
function viewPurchase(purchaseId) {
    const purchase = db.getPurchases().find(p => p.id === purchaseId);
    if (!purchase) return;

    const supplier = db.getSupplierById(purchase.supplierId);

    const content = `
        <div class="purchase-details">
            <div class="purchase-info">
                <h4>معلومات المشترى</h4>
                <div class="info-grid">
                    <div><strong>رقم الفاتورة:</strong> ${db.toArabicNumbers(purchase.invoiceNumber)}</div>
                    <div><strong>المورد:</strong> ${supplier ? supplier.name : 'مورد محذوف'}</div>
                    <div><strong>تاريخ المشترى:</strong> ${db.formatDate(purchase.purchaseDate || purchase.createdAt)}</div>
                    <div><strong>طريقة الدفع:</strong> ${purchase.paymentMethod === 'cash' ? 'نقداً' : 'آجل'}</div>
                    <div><strong>تاريخ التسجيل:</strong> ${db.formatDateTime(purchase.createdAt)}</div>
                    ${purchase.notes ? `<div><strong>ملاحظات:</strong> ${purchase.notes}</div>` : ''}
                </div>
            </div>

            <div class="purchase-items">
                <h4>أصناف المشترى</h4>
                <table class="table">
                    <thead>
                        <tr>
                            <th>المنتج</th>
                            <th>الكمية</th>
                            <th>سعر الشراء</th>
                            <th>المجموع</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${purchase.items.map(item => `
                            <tr>
                                <td>${item.productName}</td>
                                <td>${db.toArabicNumbers(item.quantity)}</td>
                                <td>${db.formatCurrency(item.price)}</td>
                                <td>${db.formatCurrency(item.quantity * item.price)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>

            <div class="purchase-summary">
                <div class="summary-row">
                    <span>المجموع الفرعي:</span>
                    <span>${db.formatCurrency(purchase.subtotal)}</span>
                </div>
                <div class="summary-row">
                    <span>الضريبة:</span>
                    <span>${db.formatCurrency(purchase.tax)}</span>
                </div>
                <div class="summary-row total-row">
                    <span><strong>المجموع الكلي:</strong></span>
                    <span><strong>${db.formatCurrency(purchase.total)}</strong></span>
                </div>
            </div>
        </div>

        <style>
            .purchase-details h4 {
                margin-bottom: 1rem;
                color: var(--primary-color);
                border-bottom: 2px solid var(--primary-color);
                padding-bottom: 0.5rem;
            }
            .info-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 1rem;
                margin-bottom: 2rem;
            }
            .purchase-items {
                margin: 2rem 0;
            }
            .purchase-summary {
                border-top: 2px solid #ddd;
                padding-top: 1rem;
                margin-top: 1rem;
            }
            .summary-row {
                display: flex;
                justify-content: space-between;
                padding: 0.25rem 0;
            }
            .total-row {
                border-top: 1px solid #333;
                margin-top: 0.5rem;
                padding-top: 0.5rem;
                font-size: 1.1rem;
            }
        </style>
    `;

    const actions = [
        {
            text: 'طباعة',
            type: 'success',
            icon: 'fas fa-print',
            handler: () => printPurchaseInvoice(purchaseId),
            close: false
        },
        {
            text: 'إغلاق',
            type: 'secondary',
            handler: () => {}
        }
    ];

    AppHelpers.createModal(`تفاصيل المشترى: ${purchase.invoiceNumber}`, content, actions);
}

// حذف مشترى
function deletePurchase(purchaseId) {
    const purchase = db.getPurchases().find(p => p.id === purchaseId);
    if (!purchase) return;

    app.showConfirm(
        `هل أنت متأكد من حذف المشترى رقم "${purchase.invoiceNumber}"؟\nسيتم تقليل كميات المنتجات المرتبطة.`,
        () => {
            // تقليل كميات المنتجات
            purchase.items.forEach(item => {
                const product = db.getProductById(item.productId);
                if (product) {
                    product.quantity = Math.max(0, product.quantity - item.quantity);
                    db.saveProduct(product);
                }
            });

            // حذف المشترى
            const purchases = db.getPurchases();
            const filteredPurchases = purchases.filter(p => p.id !== purchaseId);
            localStorage.setItem('purchases', JSON.stringify(filteredPurchases));

            app.showAlert('تم حذف المشترى بنجاح', 'success');
            loadPurchases();
        },
        'تأكيد الحذف'
    );
}

// طباعة فاتورة المشترى
function printPurchaseInvoice(purchaseId) {
    const purchase = db.getPurchases().find(p => p.id === purchaseId);
    if (!purchase) return;

    const supplier = db.getSupplierById(purchase.supplierId);
    const settings = db.getSettings();

    const content = `
        <div style="text-align: center; margin-bottom: 30px;">
            <h1>${settings.companyName}</h1>
            <p>${settings.companyAddress}</p>
            <p>هاتف: ${settings.companyPhone} | بريد: ${settings.companyEmail}</p>
        </div>

        <h2 style="text-align: center; margin-bottom: 30px;">فاتورة مشترى</h2>

        <div style="display: flex; justify-content: space-between; margin-bottom: 20px;">
            <div>
                <strong>رقم الفاتورة:</strong> ${db.toArabicNumbers(purchase.invoiceNumber)}<br>
                <strong>تاريخ المشترى:</strong> ${db.formatDate(purchase.purchaseDate || purchase.createdAt)}
            </div>
            <div>
                <strong>المورد:</strong> ${supplier ? supplier.name : 'مورد محذوف'}<br>
                <strong>طريقة الدفع:</strong> ${purchase.paymentMethod === 'cash' ? 'نقداً' : 'آجل'}
            </div>
        </div>

        <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
            <thead>
                <tr style="background-color: #f5f5f5;">
                    <th style="border: 1px solid #ddd; padding: 8px;">المنتج</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">الكمية</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">سعر الشراء</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">المجموع</th>
                </tr>
            </thead>
            <tbody>
                ${purchase.items.map(item => `
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px;">${item.productName}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${db.toArabicNumbers(item.quantity)}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${db.formatCurrency(item.price)}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${db.formatCurrency(item.quantity * item.price)}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>

        <div style="text-align: left; margin-top: 20px;">
            <p><strong>المجموع الفرعي:</strong> ${db.formatCurrency(purchase.subtotal)}</p>
            <p><strong>الضريبة (${settings.taxRate}%):</strong> ${db.formatCurrency(purchase.tax)}</p>
            <p style="font-size: 1.2em; border-top: 2px solid #333; padding-top: 10px;"><strong>المجموع الكلي:</strong> ${db.formatCurrency(purchase.total)}</p>
        </div>

        ${purchase.notes ? `<div style="margin-top: 20px;"><strong>ملاحظات:</strong> ${purchase.notes}</div>` : ''}
    `;

    app.printContent(content, `فاتورة مشترى رقم ${purchase.invoiceNumber}`);
}

// طباعة سند الاستلام
function printPurchaseReceipt(purchase) {
    const supplier = db.getSupplierById(purchase.supplierId);
    const settings = db.getSettings();

    const content = `
        <div style="text-align: center; margin-bottom: 30px;">
            <h1>${settings.companyName}</h1>
            <p>${settings.companyAddress}</p>
            <p>هاتف: ${settings.companyPhone} | بريد: ${settings.companyEmail}</p>
        </div>

        <h2 style="text-align: center; margin-bottom: 30px;">سند استلام بضائع</h2>

        <div style="margin-bottom: 20px;">
            <p><strong>رقم الفاتورة:</strong> ${db.toArabicNumbers(purchase.invoiceNumber)}</p>
            <p><strong>التاريخ:</strong> ${db.formatDateTime(purchase.createdAt)}</p>
            <p><strong>المورد:</strong> ${supplier ? supplier.name : 'غير محدد'}</p>
            <p><strong>إجمالي الأصناف:</strong> ${db.toArabicNumbers(purchase.items.length)}</p>
            <p><strong>المبلغ الإجمالي:</strong> ${db.formatCurrency(purchase.total)}</p>
        </div>

        <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
            <thead>
                <tr style="background-color: #f5f5f5;">
                    <th style="border: 1px solid #ddd; padding: 8px;">المنتج</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">الكمية المستلمة</th>
                </tr>
            </thead>
            <tbody>
                ${purchase.items.map(item => `
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px;">${item.productName}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${db.toArabicNumbers(item.quantity)}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>

        <div style="margin-top: 50px; text-align: center;">
            <p>توقيع المستلم: ________________</p>
        </div>
    `;

    app.printContent(content, `سند استلام رقم ${purchase.invoiceNumber}`);
}

// تصدير المشتريات
function exportPurchases() {
    const purchases = db.getPurchases();

    if (purchases.length === 0) {
        app.showAlert('لا توجد مشتريات للتصدير', 'warning');
        return;
    }

    const csvData = [
        ['رقم الفاتورة', 'المورد', 'عدد الأصناف', 'المبلغ الإجمالي', 'طريقة الدفع', 'التاريخ']
    ];

    purchases.forEach(purchase => {
        const supplier = db.getSupplierById(purchase.supplierId);
        csvData.push([
            purchase.invoiceNumber,
            supplier ? supplier.name : 'مورد محذوف',
            purchase.items.length,
            purchase.total,
            purchase.paymentMethod === 'cash' ? 'نقداً' : 'آجل',
            db.formatDate(purchase.createdAt)
        ]);
    });

    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `purchases_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        app.showAlert('تم تصدير المشتريات بنجاح', 'success');
    }
}

// طباعة المشتريات
function printPurchases() {
    const purchases = db.getPurchases();

    if (purchases.length === 0) {
        app.showAlert('لا توجد مشتريات للطباعة', 'warning');
        return;
    }

    const content = `
        <h2 style="text-align: center; margin-bottom: 30px;">قائمة المشتريات</h2>
        <table style="width: 100%; border-collapse: collapse;">
            <thead>
                <tr style="background-color: #f5f5f5;">
                    <th style="border: 1px solid #ddd; padding: 8px;">رقم الفاتورة</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">المورد</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">عدد الأصناف</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">المبلغ</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">التاريخ</th>
                </tr>
            </thead>
            <tbody>
                ${purchases.map(purchase => {
                    const supplier = db.getSupplierById(purchase.supplierId);
                    return `
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px;">${db.toArabicNumbers(purchase.invoiceNumber)}</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">${supplier ? supplier.name : 'مورد محذوف'}</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">${db.toArabicNumbers(purchase.items.length)}</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">${db.formatCurrency(purchase.total)}</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">${db.formatDate(purchase.createdAt)}</td>
                        </tr>
                    `;
                }).join('')}
            </tbody>
        </table>
        <div style="margin-top: 30px; text-align: center;">
            <p><strong>إجمالي المشتريات:</strong> ${db.toArabicNumbers(purchases.length)} مشترى</p>
            <p><strong>إجمالي المبلغ:</strong> ${db.formatCurrency(purchases.reduce((sum, p) => sum + p.total, 0))}</p>
        </div>
    `;

    app.printContent(content, 'قائمة المشتريات');
}

// تحديث وظيفة إضافة مشترى في suppliers.js
async function addPurchase(supplierId) {
    await showPurchaseModal();

    // تعيين المورد المحدد
    setTimeout(() => {
        const supplierSelect = document.getElementById('purchaseSupplier');
        if (supplierSelect) {
            supplierSelect.value = supplierId;
        }
    }, 100);
}