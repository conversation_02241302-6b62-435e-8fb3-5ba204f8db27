// إدارة قاعدة البيانات المحلية
class Database {
    constructor() {
        this.initializeDatabase();
    }

    // تهيئة قاعدة البيانات
    initializeDatabase() {
        // إنشاء الجداول الأساسية إذا لم تكن موجودة
        if (!localStorage.getItem('products')) {
            localStorage.setItem('products', JSON.stringify(this.getSampleProducts()));
        }
        if (!localStorage.getItem('customers')) {
            localStorage.setItem('customers', JSON.stringify([
                {
                    id: 'guest',
                    name: 'ضيف',
                    phone: '',
                    email: '',
                    address: '',
                    balance: 0,
                    createdAt: new Date().toISOString()
                },
                ...this.getSampleCustomers()
            ]));
        }
        if (!localStorage.getItem('suppliers')) {
            localStorage.setItem('suppliers', JSON.stringify(this.getSampleSuppliers()));
        }
        if (!localStorage.getItem('sales')) {
            localStorage.setItem('sales', JSON.stringify([]));
        }
        if (!localStorage.getItem('purchases')) {
            localStorage.setItem('purchases', JSON.stringify([]));
        }
        if (!localStorage.getItem('payments')) {
            localStorage.setItem('payments', JSON.stringify([]));
        }
        if (!localStorage.getItem('settings')) {
            localStorage.setItem('settings', JSON.stringify({
                companyName: 'متجر BakryAfand',
                companyAddress: 'مسقط، سلطنة عمان',
                companyPhone: '+968 9999 9999',
                companyEmail: '<EMAIL>',
                taxRate: 5,
                currency: 'ر.ع',
                password: this.hashPassword('123'),
                theme: 'light',
                autoBackup: true,
                lowStockAlert: 10
            }));
        }
    }

    // بيانات تجريبية للمنتجات
    getSampleProducts() {
        return [
            {
                id: 'prod1',
                name: 'لابتوب Dell Inspiron',
                description: 'لابتوب Dell Inspiron 15 3000 - معالج Intel Core i5',
                category: 'أجهزة كمبيوتر',
                price: 250.000,
                quantity: 15,
                barcode: '1234567890123',
                createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
                id: 'prod2',
                name: 'ماوس لاسلكي Logitech',
                description: 'ماوس لاسلكي Logitech M705 - بطارية تدوم 3 سنوات',
                category: 'ملحقات كمبيوتر',
                price: 15.500,
                quantity: 50,
                barcode: '1234567890124',
                createdAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
                id: 'prod3',
                name: 'لوحة مفاتيح ميكانيكية',
                description: 'لوحة مفاتيح ميكانيكية RGB مع إضاءة خلفية',
                category: 'ملحقات كمبيوتر',
                price: 35.750,
                quantity: 8,
                barcode: '1234567890125',
                createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
                id: 'prod4',
                name: 'شاشة Samsung 24 بوصة',
                description: 'شاشة Samsung 24 بوصة Full HD مع تقنية IPS',
                category: 'شاشات',
                price: 85.000,
                quantity: 12,
                barcode: '1234567890126',
                createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
                id: 'prod5',
                name: 'طابعة HP LaserJet',
                description: 'طابعة HP LaserJet Pro M404n - طباعة أبيض وأسود',
                category: 'طابعات',
                price: 120.000,
                quantity: 5,
                barcode: '1234567890127',
                createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
                id: 'prod6',
                name: 'هاتف iPhone 14',
                description: 'هاتف Apple iPhone 14 - 128GB - أزرق',
                category: 'هواتف ذكية',
                price: 350.000,
                quantity: 3,
                barcode: '1234567890128',
                createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString()
            }
        ];
    }

    // بيانات تجريبية للعملاء
    getSampleCustomers() {
        return [
            {
                id: 'cust1',
                name: 'أحمد محمد الشامسي',
                phone: '+968 9123 4567',
                email: '<EMAIL>',
                address: 'الخوير، مسقط',
                balance: 125.500,
                createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
                id: 'cust2',
                name: 'فاطمة علي البلوشي',
                phone: '+968 9234 5678',
                email: '<EMAIL>',
                address: 'الغبرة، مسقط',
                balance: 0,
                createdAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
                id: 'cust3',
                name: 'سالم خالد الهنائي',
                phone: '+968 9345 6789',
                email: '<EMAIL>',
                address: 'الموالح، مسقط',
                balance: 75.250,
                createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
                id: 'cust4',
                name: 'مريم سعيد الراشدي',
                phone: '+968 9456 7890',
                email: '<EMAIL>',
                address: 'الحيل، مسقط',
                balance: 0,
                createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString()
            }
        ];
    }

    // بيانات تجريبية للموردين
    getSampleSuppliers() {
        return [
            {
                id: 'supp1',
                name: 'شركة التقنية المتقدمة',
                company: 'شركة التقنية المتقدمة ش.م.م',
                phone: '+968 2456 7890',
                email: '<EMAIL>',
                address: 'الخوير، مسقط، سلطنة عمان',
                taxNumber: 'OM123456789',
                balance: 0,
                createdAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
                id: 'supp2',
                name: 'مؤسسة الحاسوب الذكي',
                company: 'مؤسسة الحاسوب الذكي',
                phone: '+968 2567 8901',
                email: '<EMAIL>',
                address: 'الغبرة، مسقط، سلطنة عمان',
                taxNumber: 'OM987654321',
                balance: 0,
                createdAt: new Date(Date.now() - 75 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
                id: 'supp3',
                name: 'شركة الإلكترونيات الحديثة',
                company: 'شركة الإلكترونيات الحديثة ذ.م.م',
                phone: '+968 2678 9012',
                email: '<EMAIL>',
                address: 'الموالح، مسقط، سلطنة عمان',
                taxNumber: 'OM456789123',
                balance: 0,
                createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString()
            }
        ];
    }

    // تشفير كلمة المرور
    hashPassword(password) {
        // تشفير بسيط - في التطبيق الحقيقي يجب استخدام تشفير أقوى
        return btoa(password);
    }

    // التحقق من كلمة المرور
    verifyPassword(password) {
        const settings = this.getSettings();
        return settings.password === this.hashPassword(password);
    }

    // تغيير كلمة المرور
    changePassword(newPassword) {
        const settings = this.getSettings();
        settings.password = this.hashPassword(newPassword);
        this.saveSettings(settings);
    }

    // الحصول على الإعدادات
    getSettings() {
        return JSON.parse(localStorage.getItem('settings'));
    }

    // حفظ الإعدادات
    saveSettings(settings) {
        localStorage.setItem('settings', JSON.stringify(settings));
    }

    // إنشاء معرف فريد
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // تحويل الأرقام إلى أرقام عربية
    toArabicNumbers(num) {
        const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        return num.toString().replace(/[0-9]/g, (digit) => arabicNumbers[digit]);
    }

    // تحويل الأرقام العربية إلى إنجليزية
    toEnglishNumbers(num) {
        const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        let result = num.toString();
        arabicNumbers.forEach((arabic, index) => {
            result = result.replace(new RegExp(arabic, 'g'), index);
        });
        return result;
    }

    // تنسيق العملة
    formatCurrency(amount) {
        const settings = this.getSettings();
        const formattedAmount = parseFloat(amount).toFixed(2);
        return `${this.toArabicNumbers(formattedAmount)} ${settings.currency}`;
    }

    // تنسيق التاريخ
    formatDate(date) {
        const d = new Date(date);
        const day = this.toArabicNumbers(d.getDate().toString().padStart(2, '0'));
        const month = this.toArabicNumbers((d.getMonth() + 1).toString().padStart(2, '0'));
        const year = this.toArabicNumbers(d.getFullYear());
        return `${day}/${month}/${year}`;
    }

    // تنسيق التاريخ والوقت
    formatDateTime(date) {
        const d = new Date(date);
        const dateStr = this.formatDate(date);
        const hours = this.toArabicNumbers(d.getHours().toString().padStart(2, '0'));
        const minutes = this.toArabicNumbers(d.getMinutes().toString().padStart(2, '0'));
        return `${dateStr} ${hours}:${minutes}`;
    }

    // عمليات المنتجات
    getProducts() {
        return JSON.parse(localStorage.getItem('products'));
    }

    saveProduct(product) {
        const products = this.getProducts();
        if (product.id) {
            const index = products.findIndex(p => p.id === product.id);
            if (index !== -1) {
                products[index] = { ...products[index], ...product, updatedAt: new Date().toISOString() };
            }
        } else {
            product.id = this.generateId();
            product.createdAt = new Date().toISOString();
            products.push(product);
        }
        localStorage.setItem('products', JSON.stringify(products));
        return product;
    }

    deleteProduct(id) {
        const products = this.getProducts();
        const filteredProducts = products.filter(p => p.id !== id);
        localStorage.setItem('products', JSON.stringify(filteredProducts));
    }

    getProductById(id) {
        const products = this.getProducts();
        return products.find(p => p.id === id);
    }

    // عمليات العملاء
    getCustomers() {
        return JSON.parse(localStorage.getItem('customers'));
    }

    saveCustomer(customer) {
        const customers = this.getCustomers();
        if (customer.id) {
            const index = customers.findIndex(c => c.id === customer.id);
            if (index !== -1) {
                customers[index] = { ...customers[index], ...customer, updatedAt: new Date().toISOString() };
            }
        } else {
            customer.id = this.generateId();
            customer.createdAt = new Date().toISOString();
            customer.balance = customer.balance || 0;
            customers.push(customer);
        }
        localStorage.setItem('customers', JSON.stringify(customers));
        return customer;
    }

    deleteCustomer(id) {
        if (id === 'guest') return; // لا يمكن حذف العميل الافتراضي
        const customers = this.getCustomers();
        const filteredCustomers = customers.filter(c => c.id !== id);
        localStorage.setItem('customers', JSON.stringify(filteredCustomers));
    }

    getCustomerById(id) {
        const customers = this.getCustomers();
        return customers.find(c => c.id === id);
    }

    updateCustomerBalance(customerId, amount) {
        const customers = this.getCustomers();
        const customer = customers.find(c => c.id === customerId);
        if (customer) {
            customer.balance = (customer.balance || 0) + amount;
            localStorage.setItem('customers', JSON.stringify(customers));
        }
    }

    // عمليات الموردين
    getSuppliers() {
        return JSON.parse(localStorage.getItem('suppliers'));
    }

    saveSupplier(supplier) {
        const suppliers = this.getSuppliers();
        if (supplier.id) {
            const index = suppliers.findIndex(s => s.id === supplier.id);
            if (index !== -1) {
                suppliers[index] = { ...suppliers[index], ...supplier, updatedAt: new Date().toISOString() };
            }
        } else {
            supplier.id = this.generateId();
            supplier.createdAt = new Date().toISOString();
            supplier.balance = supplier.balance || 0;
            suppliers.push(supplier);
        }
        localStorage.setItem('suppliers', JSON.stringify(suppliers));
        return supplier;
    }

    deleteSupplier(id) {
        const suppliers = this.getSuppliers();
        const filteredSuppliers = suppliers.filter(s => s.id !== id);
        localStorage.setItem('suppliers', JSON.stringify(filteredSuppliers));
    }

    getSupplierById(id) {
        const suppliers = this.getSuppliers();
        return suppliers.find(s => s.id === id);
    }

    // عمليات المبيعات
    getSales() {
        return JSON.parse(localStorage.getItem('sales'));
    }

    saveSale(sale) {
        const sales = this.getSales();
        sale.id = this.generateId();
        sale.createdAt = new Date().toISOString();
        sales.push(sale);
        localStorage.setItem('sales', JSON.stringify(sales));

        // تحديث المخزون
        sale.items.forEach(item => {
            const product = this.getProductById(item.productId);
            if (product) {
                product.quantity -= item.quantity;
                this.saveProduct(product);
            }
        });

        // تحديث رصيد العميل إذا كان الدفع آجل
        if (sale.paymentMethod === 'credit') {
            this.updateCustomerBalance(sale.customerId, sale.total);
        }

        return sale;
    }

    // عمليات المشتريات
    getPurchases() {
        return JSON.parse(localStorage.getItem('purchases'));
    }

    savePurchase(purchase) {
        const purchases = this.getPurchases();
        purchase.id = this.generateId();
        purchase.createdAt = new Date().toISOString();
        purchases.push(purchase);
        localStorage.setItem('purchases', JSON.stringify(purchases));

        // تحديث المخزون
        purchase.items.forEach(item => {
            const product = this.getProductById(item.productId);
            if (product) {
                product.quantity += item.quantity;
                this.saveProduct(product);
            }
        });

        return purchase;
    }

    // عمليات المدفوعات
    getPayments() {
        return JSON.parse(localStorage.getItem('payments'));
    }

    savePayment(payment) {
        const payments = this.getPayments();
        payment.id = this.generateId();
        payment.createdAt = new Date().toISOString();
        payments.push(payment);
        localStorage.setItem('payments', JSON.stringify(payments));

        // تحديث رصيد العميل
        this.updateCustomerBalance(payment.customerId, -payment.amount);

        return payment;
    }

    // إحصائيات سريعة
    getQuickStats() {
        const products = this.getProducts();
        const customers = this.getCustomers();
        const sales = this.getSales();
        const today = new Date().toDateString();

        // مبيعات اليوم
        const todaySales = sales.filter(sale => 
            new Date(sale.createdAt).toDateString() === today
        );
        const todayRevenue = todaySales.reduce((sum, sale) => sum + sale.total, 0);

        // إجمالي المبيعات
        const totalRevenue = sales.reduce((sum, sale) => sum + sale.total, 0);

        // المنتجات منخفضة المخزون
        const settings = this.getSettings();
        const lowStockProducts = products.filter(product => 
            product.quantity <= settings.lowStockAlert
        );

        // إجمالي الديون
        const totalDebts = customers.reduce((sum, customer) => 
            sum + (customer.balance > 0 ? customer.balance : 0), 0
        );

        return {
            totalProducts: products.length,
            totalCustomers: customers.length - 1, // استثناء العميل الافتراضي
            todayRevenue,
            totalRevenue,
            lowStockCount: lowStockProducts.length,
            totalDebts,
            todaySalesCount: todaySales.length,
            totalSalesCount: sales.length
        };
    }

    // تصدير البيانات
    exportData() {
        const data = {
            products: this.getProducts(),
            customers: this.getCustomers(),
            suppliers: this.getSuppliers(),
            sales: this.getSales(),
            purchases: this.getPurchases(),
            payments: this.getPayments(),
            settings: this.getSettings(),
            exportDate: new Date().toISOString()
        };
        return JSON.stringify(data, null, 2);
    }

    // استيراد البيانات
    importData(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            
            if (data.products) localStorage.setItem('products', JSON.stringify(data.products));
            if (data.customers) localStorage.setItem('customers', JSON.stringify(data.customers));
            if (data.suppliers) localStorage.setItem('suppliers', JSON.stringify(data.suppliers));
            if (data.sales) localStorage.setItem('sales', JSON.stringify(data.sales));
            if (data.purchases) localStorage.setItem('purchases', JSON.stringify(data.purchases));
            if (data.payments) localStorage.setItem('payments', JSON.stringify(data.payments));
            if (data.settings) localStorage.setItem('settings', JSON.stringify(data.settings));
            
            return true;
        } catch (error) {
            console.error('خطأ في استيراد البيانات:', error);
            return false;
        }
    }

    // مسح جميع البيانات
    clearAllData() {
        localStorage.clear();
        this.initializeDatabase();
    }
}

// إنشاء مثيل من قاعدة البيانات
const db = new Database();
