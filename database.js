// إدارة قاعدة البيانات المحلية مع تخزين دائم في ملفات JSON
class Database {
    constructor() {
        this.isElectron = typeof require !== 'undefined';
        this.ipcRenderer = this.isElectron ? require('electron').ipcRenderer : null;
        this.dataCache = {}; // كاش للبيانات لتحسين الأداء
        this.initializeDatabase();
    }

    // فحص إذا كان التطبيق يعمل في بيئة Electron
    isElectronApp() {
        return this.isElectron && this.ipcRenderer;
    }

    // قراءة البيانات من الملف أو localStorage
    async readData(fileName) {
        try {
            if (this.isElectronApp()) {
                // في بيئة Electron - قراءة من الملف
                const data = await this.ipcRenderer.invoke('read-file', `${fileName}.json`);
                return data;
            } else {
                // في المتصفح - قراءة من localStorage
                const data = localStorage.getItem(fileName);
                return data ? JSON.parse(data) : null;
            }
        } catch (error) {
            console.error(`خطأ في قراءة البيانات من ${fileName}:`, error);
            return null;
        }
    }

    // كتابة البيانات إلى الملف أو localStorage
    async writeData(fileName, data) {
        try {
            if (this.isElectronApp()) {
                // في بيئة Electron - كتابة إلى الملف
                const success = await this.ipcRenderer.invoke('write-file', `${fileName}.json`, data);
                if (success) {
                    this.dataCache[fileName] = data; // تحديث الكاش
                }
                return success;
            } else {
                // في المتصفح - كتابة إلى localStorage
                localStorage.setItem(fileName, JSON.stringify(data));
                this.dataCache[fileName] = data; // تحديث الكاش
                return true;
            }
        } catch (error) {
            console.error(`خطأ في كتابة البيانات إلى ${fileName}:`, error);
            return false;
        }
    }

    // فحص وجود الملف
    async fileExists(fileName) {
        try {
            if (this.isElectronApp()) {
                return await this.ipcRenderer.invoke('file-exists', `${fileName}.json`);
            } else {
                return localStorage.getItem(fileName) !== null;
            }
        } catch (error) {
            console.error(`خطأ في فحص وجود الملف ${fileName}:`, error);
            return false;
        }
    }

    // تهيئة قاعدة البيانات
    async initializeDatabase() {
        try {
            // إنشاء الجداول الأساسية إذا لم تكن موجودة
            if (!(await this.fileExists('products'))) {
                await this.writeData('products', this.getSampleProducts());
            }

            if (!(await this.fileExists('customers'))) {
                await this.writeData('customers', [
                    {
                        id: 'guest',
                        name: 'ضيف',
                        phone: '',
                        email: '',
                        address: '',
                        balance: 0,
                        createdAt: new Date().toISOString()
                    },
                    ...this.getSampleCustomers()
                ]);
            }

            if (!(await this.fileExists('suppliers'))) {
                await this.writeData('suppliers', this.getSampleSuppliers());
            }

            if (!(await this.fileExists('sales'))) {
                await this.writeData('sales', []);
            }

            if (!(await this.fileExists('purchases'))) {
                await this.writeData('purchases', []);
            }

            if (!(await this.fileExists('payments'))) {
                await this.writeData('payments', []);
            }

            if (!(await this.fileExists('settings'))) {
                await this.writeData('settings', {
                    companyName: 'متجر BakryAfand',
                    companyAddress: 'مسقط، سلطنة عمان',
                    companyPhone: '+968 9999 9999',
                    companyEmail: '<EMAIL>',
                    taxRate: 5,
                    currency: 'ر.ع',
                    password: this.hashPassword('123'),
                    theme: 'light',
                    autoBackup: true,
                    lowStockAlert: 10
                });
            }
        } catch (error) {
            console.error('خطأ في تهيئة قاعدة البيانات:', error);
        }
    }

    // بيانات تجريبية للمنتجات
    getSampleProducts() {
        return [
            {
                id: 'prod1',
                name: 'لابتوب Dell Inspiron',
                description: 'لابتوب Dell Inspiron 15 3000 - معالج Intel Core i5',
                category: 'أجهزة كمبيوتر',
                price: 250.000,
                quantity: 15,
                barcode: '1234567890123',
                createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
                id: 'prod2',
                name: 'ماوس لاسلكي Logitech',
                description: 'ماوس لاسلكي Logitech M705 - بطارية تدوم 3 سنوات',
                category: 'ملحقات كمبيوتر',
                price: 15.500,
                quantity: 50,
                barcode: '1234567890124',
                createdAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
                id: 'prod3',
                name: 'لوحة مفاتيح ميكانيكية',
                description: 'لوحة مفاتيح ميكانيكية RGB مع إضاءة خلفية',
                category: 'ملحقات كمبيوتر',
                price: 35.750,
                quantity: 8,
                barcode: '1234567890125',
                createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
                id: 'prod4',
                name: 'شاشة Samsung 24 بوصة',
                description: 'شاشة Samsung 24 بوصة Full HD مع تقنية IPS',
                category: 'شاشات',
                price: 85.000,
                quantity: 12,
                barcode: '1234567890126',
                createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
                id: 'prod5',
                name: 'طابعة HP LaserJet',
                description: 'طابعة HP LaserJet Pro M404n - طباعة أبيض وأسود',
                category: 'طابعات',
                price: 120.000,
                quantity: 5,
                barcode: '1234567890127',
                createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
                id: 'prod6',
                name: 'هاتف iPhone 14',
                description: 'هاتف Apple iPhone 14 - 128GB - أزرق',
                category: 'هواتف ذكية',
                price: 350.000,
                quantity: 3,
                barcode: '1234567890128',
                createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString()
            }
        ];
    }

    // بيانات تجريبية للعملاء
    getSampleCustomers() {
        return [
            {
                id: 'cust1',
                name: 'أحمد محمد الشامسي',
                phone: '+968 9123 4567',
                email: '<EMAIL>',
                address: 'الخوير، مسقط',
                balance: 125.500,
                createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
                id: 'cust2',
                name: 'فاطمة علي البلوشي',
                phone: '+968 9234 5678',
                email: '<EMAIL>',
                address: 'الغبرة، مسقط',
                balance: 0,
                createdAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
                id: 'cust3',
                name: 'سالم خالد الهنائي',
                phone: '+968 9345 6789',
                email: '<EMAIL>',
                address: 'الموالح، مسقط',
                balance: 75.250,
                createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
                id: 'cust4',
                name: 'مريم سعيد الراشدي',
                phone: '+968 9456 7890',
                email: '<EMAIL>',
                address: 'الحيل، مسقط',
                balance: 0,
                createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString()
            }
        ];
    }

    // بيانات تجريبية للموردين
    getSampleSuppliers() {
        return [
            {
                id: 'supp1',
                name: 'شركة التقنية المتقدمة',
                company: 'شركة التقنية المتقدمة ش.م.م',
                phone: '+968 2456 7890',
                email: '<EMAIL>',
                address: 'الخوير، مسقط، سلطنة عمان',
                taxNumber: 'OM123456789',
                balance: 0,
                createdAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
                id: 'supp2',
                name: 'مؤسسة الحاسوب الذكي',
                company: 'مؤسسة الحاسوب الذكي',
                phone: '+968 2567 8901',
                email: '<EMAIL>',
                address: 'الغبرة، مسقط، سلطنة عمان',
                taxNumber: 'OM987654321',
                balance: 0,
                createdAt: new Date(Date.now() - 75 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
                id: 'supp3',
                name: 'شركة الإلكترونيات الحديثة',
                company: 'شركة الإلكترونيات الحديثة ذ.م.م',
                phone: '+968 2678 9012',
                email: '<EMAIL>',
                address: 'الموالح، مسقط، سلطنة عمان',
                taxNumber: 'OM456789123',
                balance: 0,
                createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString()
            }
        ];
    }

    // تشفير كلمة المرور
    hashPassword(password) {
        // تشفير بسيط - في التطبيق الحقيقي يجب استخدام تشفير أقوى
        return btoa(password);
    }

    // التحقق من كلمة المرور
    async verifyPassword(password) {
        try {
            const settings = await this.getSettings();
            return settings && settings.password === this.hashPassword(password);
        } catch (error) {
            console.error('خطأ في التحقق من كلمة المرور:', error);
            return false;
        }
    }

    // تغيير كلمة المرور
    async changePassword(newPassword) {
        try {
            const settings = await this.getSettings();
            if (settings) {
                settings.password = this.hashPassword(newPassword);
                return await this.saveSettings(settings);
            }
            return false;
        } catch (error) {
            console.error('خطأ في تغيير كلمة المرور:', error);
            return false;
        }
    }

    // الحصول على الإعدادات
    async getSettings() {
        try {
            // التحقق من الكاش أولاً
            if (this.dataCache['settings']) {
                return this.dataCache['settings'];
            }

            const settings = await this.readData('settings');
            if (settings) {
                this.dataCache['settings'] = settings;
                return settings;
            }

            // إرجاع إعدادات افتراضية إذا لم توجد
            const defaultSettings = {
                companyName: 'متجر BakryAfand',
                companyAddress: 'مسقط، سلطنة عمان',
                companyPhone: '+968 9999 9999',
                companyEmail: '<EMAIL>',
                taxRate: 5,
                currency: 'ر.ع',
                password: this.hashPassword('123'),
                theme: 'light',
                autoBackup: true,
                lowStockAlert: 10
            };
            await this.writeData('settings', defaultSettings);
            return defaultSettings;
        } catch (error) {
            console.error('خطأ في الحصول على الإعدادات:', error);
            return null;
        }
    }

    // حفظ الإعدادات
    async saveSettings(settings) {
        try {
            const success = await this.writeData('settings', settings);
            if (success) {
                this.dataCache['settings'] = settings;
            }
            return success;
        } catch (error) {
            console.error('خطأ في حفظ الإعدادات:', error);
            return false;
        }
    }

    // إنشاء معرف فريد
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // تحويل الأرقام إلى أرقام عربية
    toArabicNumbers(num) {
        const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        return num.toString().replace(/[0-9]/g, (digit) => arabicNumbers[digit]);
    }

    // تحويل الأرقام العربية إلى إنجليزية
    toEnglishNumbers(num) {
        const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        let result = num.toString();
        arabicNumbers.forEach((arabic, index) => {
            result = result.replace(new RegExp(arabic, 'g'), index);
        });
        return result;
    }

    // تنسيق العملة
    async formatCurrency(amount) {
        try {
            const settings = await this.getSettings();
            const formattedAmount = parseFloat(amount).toFixed(2);
            return `${this.toArabicNumbers(formattedAmount)} ${settings?.currency || 'ر.ع'}`;
        } catch (error) {
            console.error('خطأ في تنسيق العملة:', error);
            const formattedAmount = parseFloat(amount).toFixed(2);
            return `${this.toArabicNumbers(formattedAmount)} ر.ع`;
        }
    }

    // تنسيق العملة (نسخة متزامنة للاستخدام مع البيانات المحفوظة مسبقاً)
    formatCurrencySync(amount, currency = 'ر.ع') {
        const formattedAmount = parseFloat(amount).toFixed(2);
        return `${this.toArabicNumbers(formattedAmount)} ${currency}`;
    }

    // تنسيق التاريخ
    formatDate(date) {
        const d = new Date(date);
        const day = this.toArabicNumbers(d.getDate().toString().padStart(2, '0'));
        const month = this.toArabicNumbers((d.getMonth() + 1).toString().padStart(2, '0'));
        const year = this.toArabicNumbers(d.getFullYear());
        return `${day}/${month}/${year}`;
    }

    // تنسيق التاريخ والوقت
    formatDateTime(date) {
        const d = new Date(date);
        const dateStr = this.formatDate(date);
        const hours = this.toArabicNumbers(d.getHours().toString().padStart(2, '0'));
        const minutes = this.toArabicNumbers(d.getMinutes().toString().padStart(2, '0'));
        return `${dateStr} ${hours}:${minutes}`;
    }

    // عمليات المنتجات
    async getProducts() {
        try {
            // التحقق من الكاش أولاً
            if (this.dataCache['products']) {
                return this.dataCache['products'];
            }

            const products = await this.readData('products');
            if (products) {
                this.dataCache['products'] = products;
                return products;
            }

            // إرجاع قائمة فارغة إذا لم توجد منتجات
            const emptyProducts = [];
            await this.writeData('products', emptyProducts);
            return emptyProducts;
        } catch (error) {
            console.error('خطأ في الحصول على المنتجات:', error);
            return [];
        }
    }

    async saveProduct(product) {
        try {
            const products = await this.getProducts();
            if (product.id) {
                const index = products.findIndex(p => p.id === product.id);
                if (index !== -1) {
                    products[index] = { ...products[index], ...product, updatedAt: new Date().toISOString() };
                }
            } else {
                product.id = this.generateId();
                product.createdAt = new Date().toISOString();
                products.push(product);
            }

            const success = await this.writeData('products', products);
            if (success) {
                this.dataCache['products'] = products;
            }
            return success ? product : null;
        } catch (error) {
            console.error('خطأ في حفظ المنتج:', error);
            return null;
        }
    }

    async deleteProduct(id) {
        try {
            const products = await this.getProducts();
            const filteredProducts = products.filter(p => p.id !== id);
            const success = await this.writeData('products', filteredProducts);
            if (success) {
                this.dataCache['products'] = filteredProducts;
            }
            return success;
        } catch (error) {
            console.error('خطأ في حذف المنتج:', error);
            return false;
        }
    }

    async getProductById(id) {
        try {
            const products = await this.getProducts();
            return products.find(p => p.id === id);
        } catch (error) {
            console.error('خطأ في الحصول على المنتج:', error);
            return null;
        }
    }

    // عمليات العملاء
    async getCustomers() {
        try {
            // التحقق من الكاش أولاً
            if (this.dataCache['customers']) {
                return this.dataCache['customers'];
            }

            const customers = await this.readData('customers');
            if (customers) {
                this.dataCache['customers'] = customers;
                return customers;
            }

            // إرجاع قائمة فارغة إذا لم توجد عملاء
            const emptyCustomers = [];
            await this.writeData('customers', emptyCustomers);
            return emptyCustomers;
        } catch (error) {
            console.error('خطأ في الحصول على العملاء:', error);
            return [];
        }
    }

    async saveCustomer(customer) {
        try {
            const customers = await this.getCustomers();
            if (customer.id) {
                const index = customers.findIndex(c => c.id === customer.id);
                if (index !== -1) {
                    customers[index] = { ...customers[index], ...customer, updatedAt: new Date().toISOString() };
                }
            } else {
                customer.id = this.generateId();
                customer.createdAt = new Date().toISOString();
                customer.balance = customer.balance || 0;
                customers.push(customer);
            }

            const success = await this.writeData('customers', customers);
            if (success) {
                this.dataCache['customers'] = customers;
            }
            return success ? customer : null;
        } catch (error) {
            console.error('خطأ في حفظ العميل:', error);
            return null;
        }
    }

    async deleteCustomer(id) {
        try {
            if (id === 'guest') return false; // لا يمكن حذف العميل الافتراضي

            const customers = await this.getCustomers();
            const filteredCustomers = customers.filter(c => c.id !== id);
            const success = await this.writeData('customers', filteredCustomers);
            if (success) {
                this.dataCache['customers'] = filteredCustomers;
            }
            return success;
        } catch (error) {
            console.error('خطأ في حذف العميل:', error);
            return false;
        }
    }

    async getCustomerById(id) {
        try {
            const customers = await this.getCustomers();
            return customers.find(c => c.id === id);
        } catch (error) {
            console.error('خطأ في الحصول على العميل:', error);
            return null;
        }
    }

    async updateCustomerBalance(customerId, amount) {
        try {
            const customers = await this.getCustomers();
            const customer = customers.find(c => c.id === customerId);
            if (customer) {
                customer.balance = (customer.balance || 0) + amount;
                const success = await this.writeData('customers', customers);
                if (success) {
                    this.dataCache['customers'] = customers;
                }
                return success;
            }
            return false;
        } catch (error) {
            console.error('خطأ في تحديث رصيد العميل:', error);
            return false;
        }
    }

    // عمليات الموردين
    async getSuppliers() {
        try {
            // التحقق من الكاش أولاً
            if (this.dataCache['suppliers']) {
                return this.dataCache['suppliers'];
            }

            const suppliers = await this.readData('suppliers');
            if (suppliers) {
                this.dataCache['suppliers'] = suppliers;
                return suppliers;
            }

            // إرجاع قائمة فارغة إذا لم توجد موردين
            const emptySuppliers = [];
            await this.writeData('suppliers', emptySuppliers);
            return emptySuppliers;
        } catch (error) {
            console.error('خطأ في الحصول على الموردين:', error);
            return [];
        }
    }

    async saveSupplier(supplier) {
        try {
            const suppliers = await this.getSuppliers();
            if (supplier.id) {
                const index = suppliers.findIndex(s => s.id === supplier.id);
                if (index !== -1) {
                    suppliers[index] = { ...suppliers[index], ...supplier, updatedAt: new Date().toISOString() };
                }
            } else {
                supplier.id = this.generateId();
                supplier.createdAt = new Date().toISOString();
                supplier.balance = supplier.balance || 0;
                suppliers.push(supplier);
            }

            const success = await this.writeData('suppliers', suppliers);
            if (success) {
                this.dataCache['suppliers'] = suppliers;
            }
            return success ? supplier : null;
        } catch (error) {
            console.error('خطأ في حفظ المورد:', error);
            return null;
        }
    }

    async deleteSupplier(id) {
        try {
            const suppliers = await this.getSuppliers();
            const filteredSuppliers = suppliers.filter(s => s.id !== id);
            const success = await this.writeData('suppliers', filteredSuppliers);
            if (success) {
                this.dataCache['suppliers'] = filteredSuppliers;
            }
            return success;
        } catch (error) {
            console.error('خطأ في حذف المورد:', error);
            return false;
        }
    }

    async getSupplierById(id) {
        try {
            const suppliers = await this.getSuppliers();
            return suppliers.find(s => s.id === id);
        } catch (error) {
            console.error('خطأ في الحصول على المورد:', error);
            return null;
        }
    }

    // عمليات المبيعات
    async getSales() {
        try {
            // التحقق من الكاش أولاً
            if (this.dataCache['sales']) {
                return this.dataCache['sales'];
            }

            const sales = await this.readData('sales');
            if (sales) {
                this.dataCache['sales'] = sales;
                return sales;
            }

            // إرجاع قائمة فارغة إذا لم توجد مبيعات
            const emptySales = [];
            await this.writeData('sales', emptySales);
            return emptySales;
        } catch (error) {
            console.error('خطأ في الحصول على المبيعات:', error);
            return [];
        }
    }

    async saveSale(sale) {
        try {
            const sales = await this.getSales();
            sale.id = this.generateId();
            sale.createdAt = new Date().toISOString();
            sales.push(sale);

            const success = await this.writeData('sales', sales);
            if (!success) {
                throw new Error('فشل في حفظ المبيعة');
            }

            this.dataCache['sales'] = sales;

            // تحديث المخزون
            for (const item of sale.items) {
                const product = await this.getProductById(item.productId);
                if (product) {
                    product.quantity -= item.quantity;
                    await this.saveProduct(product);
                }
            }

            // تحديث رصيد العميل إذا كان الدفع آجل
            if (sale.paymentMethod === 'credit') {
                await this.updateCustomerBalance(sale.customerId, sale.total);
            }

            return sale;
        } catch (error) {
            console.error('خطأ في حفظ المبيعة:', error);
            return null;
        }
    }

    // عمليات المشتريات
    async getPurchases() {
        try {
            // التحقق من الكاش أولاً
            if (this.dataCache['purchases']) {
                return this.dataCache['purchases'];
            }

            const purchases = await this.readData('purchases');
            if (purchases) {
                this.dataCache['purchases'] = purchases;
                return purchases;
            }

            // إرجاع قائمة فارغة إذا لم توجد مشتريات
            const emptyPurchases = [];
            await this.writeData('purchases', emptyPurchases);
            return emptyPurchases;
        } catch (error) {
            console.error('خطأ في الحصول على المشتريات:', error);
            return [];
        }
    }

    async savePurchase(purchase) {
        try {
            const purchases = await this.getPurchases();
            purchase.id = this.generateId();
            purchase.createdAt = new Date().toISOString();
            purchases.push(purchase);

            const success = await this.writeData('purchases', purchases);
            if (!success) {
                throw new Error('فشل في حفظ المشترى');
            }

            this.dataCache['purchases'] = purchases;

            // تحديث المخزون
            for (const item of purchase.items) {
                const product = await this.getProductById(item.productId);
                if (product) {
                    product.quantity += item.quantity;
                    await this.saveProduct(product);
                }
            }

            return purchase;
        } catch (error) {
            console.error('خطأ في حفظ المشترى:', error);
            return null;
        }
    }

    // عمليات المدفوعات
    async getPayments() {
        try {
            // التحقق من الكاش أولاً
            if (this.dataCache['payments']) {
                return this.dataCache['payments'];
            }

            const payments = await this.readData('payments');
            if (payments) {
                this.dataCache['payments'] = payments;
                return payments;
            }

            // إرجاع قائمة فارغة إذا لم توجد مدفوعات
            const emptyPayments = [];
            await this.writeData('payments', emptyPayments);
            return emptyPayments;
        } catch (error) {
            console.error('خطأ في الحصول على المدفوعات:', error);
            return [];
        }
    }

    async savePayment(payment) {
        try {
            const payments = await this.getPayments();
            payment.id = this.generateId();
            payment.createdAt = new Date().toISOString();
            payments.push(payment);

            const success = await this.writeData('payments', payments);
            if (!success) {
                throw new Error('فشل في حفظ المدفوعة');
            }

            this.dataCache['payments'] = payments;

            // تحديث رصيد العميل
            await this.updateCustomerBalance(payment.customerId, -payment.amount);

            return payment;
        } catch (error) {
            console.error('خطأ في حفظ المدفوعة:', error);
            return null;
        }
    }

    // إحصائيات سريعة
    async getQuickStats() {
        try {
            const products = await this.getProducts();
            const customers = await this.getCustomers();
            const sales = await this.getSales();
            const settings = await this.getSettings();
            const today = new Date().toDateString();

            // مبيعات اليوم
            const todaySales = sales.filter(sale =>
                new Date(sale.createdAt).toDateString() === today
            );
            const todayRevenue = todaySales.reduce((sum, sale) => sum + sale.total, 0);

            // إجمالي المبيعات
            const totalRevenue = sales.reduce((sum, sale) => sum + sale.total, 0);

            // المنتجات منخفضة المخزون
            const lowStockProducts = products.filter(product =>
                product.quantity <= (settings?.lowStockAlert || 10)
            );

            // إجمالي الديون
            const totalDebts = customers.reduce((sum, customer) =>
                sum + (customer.balance > 0 ? customer.balance : 0), 0
            );

            return {
                totalProducts: products.length,
                totalCustomers: customers.length - 1, // استثناء العميل الافتراضي
                todayRevenue,
                totalRevenue,
                lowStockCount: lowStockProducts.length,
                totalDebts,
                todaySalesCount: todaySales.length,
                totalSalesCount: sales.length
            };
        } catch (error) {
            console.error('خطأ في الحصول على الإحصائيات:', error);
            return {
                totalProducts: 0,
                totalCustomers: 0,
                todayRevenue: 0,
                totalRevenue: 0,
                lowStockCount: 0,
                totalDebts: 0,
                todaySalesCount: 0,
                totalSalesCount: 0
            };
        }
    }

    // تصدير البيانات
    async exportData() {
        try {
            const data = {
                products: await this.getProducts(),
                customers: await this.getCustomers(),
                suppliers: await this.getSuppliers(),
                sales: await this.getSales(),
                purchases: await this.getPurchases(),
                payments: await this.getPayments(),
                settings: await this.getSettings(),
                exportDate: new Date().toISOString()
            };
            return JSON.stringify(data, null, 2);
        } catch (error) {
            console.error('خطأ في تصدير البيانات:', error);
            return null;
        }
    }

    // استيراد البيانات
    async importData(jsonData) {
        try {
            const data = JSON.parse(jsonData);

            // مسح الكاش قبل الاستيراد
            this.dataCache = {};

            if (data.products) await this.writeData('products', data.products);
            if (data.customers) await this.writeData('customers', data.customers);
            if (data.suppliers) await this.writeData('suppliers', data.suppliers);
            if (data.sales) await this.writeData('sales', data.sales);
            if (data.purchases) await this.writeData('purchases', data.purchases);
            if (data.payments) await this.writeData('payments', data.payments);
            if (data.settings) await this.writeData('settings', data.settings);

            return true;
        } catch (error) {
            console.error('خطأ في استيراد البيانات:', error);
            return false;
        }
    }

    // مسح جميع البيانات نهائياً (بدون إعادة تهيئة)
    async clearAllData() {
        try {
            // مسح الكاش
            this.dataCache = {};

            if (this.isElectronApp()) {
                // في بيئة Electron - حذف الملفات
                const fileNames = ['products', 'customers', 'suppliers', 'sales', 'purchases', 'payments', 'settings'];
                for (const fileName of fileNames) {
                    await this.ipcRenderer.invoke('delete-file', `${fileName}.json`);
                }

                // حذف مجلد النسخ الاحتياطية أيضاً
                try {
                    const backupFiles = await this.ipcRenderer.invoke('list-backups');
                    for (const backup of backupFiles) {
                        await this.ipcRenderer.invoke('delete-file', `backups/${backup.name}`);
                    }
                } catch (backupError) {
                    console.warn('تعذر حذف النسخ الاحتياطية:', backupError);
                }
            } else {
                // في المتصفح - مسح localStorage
                localStorage.clear();
            }

            // لا نعيد تهيئة قاعدة البيانات - البيانات محذوفة نهائياً
            return true;
        } catch (error) {
            console.error('خطأ في مسح البيانات:', error);
            return false;
        }
    }



    // إنشاء نسخة احتياطية
    async createBackup() {
        try {
            if (this.isElectronApp()) {
                const backupData = await this.exportData();
                if (backupData) {
                    const result = await this.ipcRenderer.invoke('create-backup', JSON.parse(backupData));
                    return result;
                }
            }
            return { success: false, error: 'النسخ الاحتياطي متاح فقط في تطبيق Electron' };
        } catch (error) {
            console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
            return { success: false, error: error.message };
        }
    }

    // استعادة نسخة احتياطية
    async restoreBackup(backupFileName) {
        try {
            if (this.isElectronApp()) {
                const result = await this.ipcRenderer.invoke('restore-backup', backupFileName);
                if (result.success) {
                    return await this.importData(JSON.stringify(result.data));
                }
                return false;
            }
            return false;
        } catch (error) {
            console.error('خطأ في استعادة النسخة الاحتياطية:', error);
            return false;
        }
    }

    // قائمة النسخ الاحتياطية
    async listBackups() {
        try {
            if (this.isElectronApp()) {
                return await this.ipcRenderer.invoke('list-backups');
            }
            return [];
        } catch (error) {
            console.error('خطأ في قراءة قائمة النسخ الاحتياطية:', error);
            return [];
        }
    }
}

// إنشاء مثيل من قاعدة البيانات
let db;

// تهيئة قاعدة البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', async () => {
    try {
        db = new Database();
        console.log('تم تهيئة قاعدة البيانات بنجاح');

        // إشعار باقي التطبيق أن قاعدة البيانات جاهزة
        window.dispatchEvent(new CustomEvent('databaseReady'));
    } catch (error) {
        console.error('خطأ في تهيئة قاعدة البيانات:', error);
    }
});
