// لوحة المعلومات الرئيسية
function loadDashboard() {
    const pageContent = document.getElementById('pageContent');
    
    // الحصول على الإحصائيات
    const stats = db.getQuickStats();
    const settings = db.getSettings();
    
    // إنشاء شبكة الإحصائيات
    const statsGrid = document.createElement('div');
    statsGrid.className = 'stats-grid';
    
    // بطاقات الإحصائيات
    const statCards = [
        {
            icon: 'fas fa-box',
            value: db.toArabicNumbers(stats.totalProducts),
            label: 'إجمالي المنتجات',
            color: 'primary'
        },
        {
            icon: 'fas fa-users',
            value: db.toArabicNumbers(stats.totalCustomers),
            label: 'إجمالي العملاء',
            color: 'info'
        },
        {
            icon: 'fas fa-money-bill-wave',
            value: db.formatCurrency(stats.todayRevenue),
            label: 'مبيعات اليوم',
            color: 'success'
        },
        {
            icon: 'fas fa-chart-line',
            value: db.formatCurrency(stats.totalRevenue),
            label: 'إجمالي المبيعات',
            color: 'warning'
        },
        {
            icon: 'fas fa-exclamation-triangle',
            value: db.toArabicNumbers(stats.lowStockCount),
            label: 'منتجات منخفضة المخزون',
            color: 'danger'
        },
        {
            icon: 'fas fa-credit-card',
            value: db.formatCurrency(stats.totalDebts),
            label: 'إجمالي الديون',
            color: 'secondary'
        }
    ];
    
    statCards.forEach(card => {
        const statCard = AppHelpers.createStatCard(card.icon, card.value, card.label, card.color);
        statsGrid.appendChild(statCard);
    });
    
    pageContent.appendChild(statsGrid);
    
    // إنشاء صف للمحتوى
    const contentRow = document.createElement('div');
    contentRow.style.display = 'grid';
    contentRow.style.gridTemplateColumns = '1fr 1fr';
    contentRow.style.gap = '1.5rem';
    contentRow.style.marginTop = '2rem';
    
    // المبيعات الأخيرة
    const recentSalesCard = createRecentSalesCard();
    contentRow.appendChild(recentSalesCard);
    
    // المنتجات منخفضة المخزون
    const lowStockCard = createLowStockCard();
    contentRow.appendChild(lowStockCard);
    
    pageContent.appendChild(contentRow);
    
    // إنشاء صف آخر
    const secondRow = document.createElement('div');
    secondRow.style.display = 'grid';
    secondRow.style.gridTemplateColumns = '1fr 1fr';
    secondRow.style.gap = '1.5rem';
    secondRow.style.marginTop = '1.5rem';
    
    // أفضل العملاء
    const topCustomersCard = createTopCustomersCard();
    secondRow.appendChild(topCustomersCard);
    
    // مخطط المبيعات الشهرية
    const salesChartCard = createSalesChartCard();
    secondRow.appendChild(salesChartCard);
    
    pageContent.appendChild(secondRow);
    
    // التنبيهات
    const alertsCard = createAlertsCard();
    pageContent.appendChild(alertsCard);
}

// إنشاء بطاقة المبيعات الأخيرة
function createRecentSalesCard() {
    const sales = db.getSales().slice(-5).reverse(); // آخر 5 مبيعات
    
    const content = document.createElement('div');
    
    if (sales.length === 0) {
        content.innerHTML = '<p class="text-center">لا توجد مبيعات حتى الآن</p>';
    } else {
        const table = document.createElement('table');
        table.className = 'table';
        
        table.innerHTML = `
            <thead>
                <tr>
                    <th>رقم الفاتورة</th>
                    <th>العميل</th>
                    <th>المبلغ</th>
                    <th>التاريخ</th>
                </tr>
            </thead>
            <tbody>
                ${sales.map(sale => {
                    const customer = db.getCustomerById(sale.customerId);
                    return `
                        <tr>
                            <td>${db.toArabicNumbers(sale.invoiceNumber || sale.id.slice(-6))}</td>
                            <td>${customer ? customer.name : 'غير محدد'}</td>
                            <td>${db.formatCurrency(sale.total)}</td>
                            <td>${db.formatDate(sale.createdAt)}</td>
                        </tr>
                    `;
                }).join('')}
            </tbody>
        `;
        
        content.appendChild(table);
    }
    
    const actions = [
        {
            text: 'عرض الكل',
            icon: 'fas fa-eye',
            type: 'primary',
            handler: () => app.navigateToPage('sales')
        }
    ];
    
    return AppHelpers.createCard('المبيعات الأخيرة', content, actions);
}

// إنشاء بطاقة المنتجات منخفضة المخزون
function createLowStockCard() {
    const products = db.getProducts();
    const settings = db.getSettings();
    const lowStockProducts = products.filter(product => 
        product.quantity <= settings.lowStockAlert
    ).slice(0, 5);
    
    const content = document.createElement('div');
    
    if (lowStockProducts.length === 0) {
        content.innerHTML = '<p class="text-center">جميع المنتجات متوفرة بكميات كافية</p>';
    } else {
        const table = document.createElement('table');
        table.className = 'table';
        
        table.innerHTML = `
            <thead>
                <tr>
                    <th>اسم المنتج</th>
                    <th>الكمية المتبقية</th>
                    <th>الحد الأدنى</th>
                </tr>
            </thead>
            <tbody>
                ${lowStockProducts.map(product => `
                    <tr>
                        <td>${product.name}</td>
                        <td><span class="badge badge-danger">${db.toArabicNumbers(product.quantity)}</span></td>
                        <td>${db.toArabicNumbers(settings.lowStockAlert)}</td>
                    </tr>
                `).join('')}
            </tbody>
        `;
        
        content.appendChild(table);
    }
    
    const actions = [
        {
            text: 'إدارة المنتجات',
            icon: 'fas fa-box',
            type: 'warning',
            handler: () => app.navigateToPage('products')
        }
    ];
    
    return AppHelpers.createCard('تنبيهات المخزون', content, actions);
}

// إنشاء بطاقة أفضل العملاء
function createTopCustomersCard() {
    const sales = db.getSales();
    const customers = db.getCustomers();
    
    // حساب مبيعات كل عميل
    const customerSales = {};
    sales.forEach(sale => {
        if (!customerSales[sale.customerId]) {
            customerSales[sale.customerId] = 0;
        }
        customerSales[sale.customerId] += sale.total;
    });
    
    // ترتيب العملاء حسب المبيعات
    const topCustomers = Object.entries(customerSales)
        .map(([customerId, total]) => {
            const customer = customers.find(c => c.id === customerId);
            return {
                customer: customer ? customer.name : 'غير محدد',
                total: total
            };
        })
        .sort((a, b) => b.total - a.total)
        .slice(0, 5);
    
    const content = document.createElement('div');
    
    if (topCustomers.length === 0) {
        content.innerHTML = '<p class="text-center">لا توجد مبيعات للعملاء حتى الآن</p>';
    } else {
        const table = document.createElement('table');
        table.className = 'table';
        
        table.innerHTML = `
            <thead>
                <tr>
                    <th>اسم العميل</th>
                    <th>إجمالي المشتريات</th>
                </tr>
            </thead>
            <tbody>
                ${topCustomers.map(item => `
                    <tr>
                        <td>${item.customer}</td>
                        <td>${db.formatCurrency(item.total)}</td>
                    </tr>
                `).join('')}
            </tbody>
        `;
        
        content.appendChild(table);
    }
    
    const actions = [
        {
            text: 'إدارة العملاء',
            icon: 'fas fa-users',
            type: 'info',
            handler: () => app.navigateToPage('customers')
        }
    ];
    
    return AppHelpers.createCard('أفضل العملاء', content, actions);
}

// إنشاء بطاقة مخطط المبيعات
function createSalesChartCard() {
    const sales = db.getSales();
    const now = new Date();
    const last7Days = [];
    
    // إنشاء بيانات آخر 7 أيام
    for (let i = 6; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        const dateString = date.toDateString();
        
        const daySales = sales.filter(sale => 
            new Date(sale.createdAt).toDateString() === dateString
        );
        
        const dayTotal = daySales.reduce((sum, sale) => sum + sale.total, 0);
        
        last7Days.push({
            label: db.toArabicNumbers(date.getDate()),
            value: dayTotal
        });
    }
    
    const content = document.createElement('div');
    content.style.textAlign = 'center';
    
    if (last7Days.every(day => day.value === 0)) {
        content.innerHTML = '<p>لا توجد مبيعات في الأيام السابقة</p>';
    } else {
        const chart = AppHelpers.createSimpleChart(last7Days);
        content.appendChild(chart);
        
        const chartInfo = document.createElement('div');
        chartInfo.style.marginTop = '1rem';
        chartInfo.innerHTML = '<small>مبيعات آخر ٧ أيام</small>';
        content.appendChild(chartInfo);
    }
    
    const actions = [
        {
            text: 'التقارير التفصيلية',
            icon: 'fas fa-chart-bar',
            type: 'success',
            handler: () => app.navigateToPage('reports')
        }
    ];
    
    return AppHelpers.createCard('مخطط المبيعات', content, actions);
}

// إنشاء بطاقة التنبيهات
function createAlertsCard() {
    const alerts = [];
    const stats = db.getQuickStats();
    const settings = db.getSettings();
    
    // تنبيهات المخزون المنخفض
    if (stats.lowStockCount > 0) {
        alerts.push({
            type: 'warning',
            icon: 'fas fa-exclamation-triangle',
            message: `يوجد ${db.toArabicNumbers(stats.lowStockCount)} منتج بمخزون منخفض`
        });
    }
    
    // تنبيهات الديون
    if (stats.totalDebts > 0) {
        alerts.push({
            type: 'info',
            icon: 'fas fa-credit-card',
            message: `إجمالي الديون المستحقة: ${db.formatCurrency(stats.totalDebts)}`
        });
    }
    
    // تنبيه عدم وجود مبيعات اليوم
    if (stats.todayRevenue === 0) {
        alerts.push({
            type: 'info',
            icon: 'fas fa-info-circle',
            message: 'لم يتم تسجيل أي مبيعات اليوم'
        });
    }
    
    const content = document.createElement('div');
    
    if (alerts.length === 0) {
        content.innerHTML = `
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                لا توجد تنبيهات في الوقت الحالي - كل شيء يسير بشكل طبيعي
            </div>
        `;
    } else {
        alerts.forEach(alert => {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${alert.type}`;
            alertDiv.innerHTML = `
                <i class="${alert.icon}"></i>
                ${alert.message}
            `;
            content.appendChild(alertDiv);
        });
    }
    
    return AppHelpers.createCard('التنبيهات والإشعارات', content);
}

// تحديث لوحة المعلومات كل دقيقة
setInterval(() => {
    if (app && app.currentPage === 'dashboard') {
        loadDashboard();
    }
}, 60000);
