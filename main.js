// الملف الرئيسي للتطبيق
class App {
    constructor() {
        this.currentPage = 'dashboard';
        this.isLoggedIn = false;
        this.init();
    }

    // تهيئة التطبيق
    init() {
        this.checkLoginStatus();
        this.setupEventListeners();
        this.setupModals();
    }

    // فحص حالة تسجيل الدخول
    checkLoginStatus() {
        const loginStatus = localStorage.getItem('isLoggedIn');
        if (loginStatus === 'true') {
            this.isLoggedIn = true;
            this.showMainApp();
        } else {
            this.showLoginScreen();
        }
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // تسجيل الدخول
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        // تسجيل الخروج
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => this.handleLogout());
        }

        // التنقل في القائمة الجانبية
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => this.handleNavigation(e));
        });

        // تبديل القائمة الجانبية
        const sidebarToggle = document.getElementById('sidebarToggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => this.toggleSidebar());
        }

        // إغلاق القائمة الجانبية عند النقر خارجها في الشاشات الصغيرة
        document.addEventListener('click', (e) => {
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');
            
            if (window.innerWidth <= 768 && 
                !sidebar.contains(e.target) && 
                !sidebarToggle.contains(e.target)) {
                sidebar.classList.remove('show');
            }
        });

        // التعامل مع تغيير حجم الشاشة
        window.addEventListener('resize', () => {
            const sidebar = document.getElementById('sidebar');
            if (window.innerWidth > 768) {
                sidebar.classList.remove('show');
            }
        });
    }

    // إعداد النوافذ المنبثقة
    setupModals() {
        // إغلاق النوافذ المنبثقة
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal') || e.target.classList.contains('modal-close')) {
                this.closeAllModals();
            }
        });

        // أزرار التأكيد
        const confirmYes = document.getElementById('confirmYes');
        const confirmNo = document.getElementById('confirmNo');
        const alertOk = document.getElementById('alertOk');

        if (confirmNo) {
            confirmNo.addEventListener('click', () => this.closeAllModals());
        }

        if (alertOk) {
            alertOk.addEventListener('click', () => this.closeAllModals());
        }
    }

    // معالجة تسجيل الدخول
    handleLogin(e) {
        e.preventDefault();
        const password = document.getElementById('password').value;

        if (db.verifyPassword(password)) {
            this.isLoggedIn = true;
            localStorage.setItem('isLoggedIn', 'true');
            this.showMainApp();
            this.showAlert('تم تسجيل الدخول بنجاح', 'success');
        } else {
            this.showAlert('كلمة المرور غير صحيحة', 'danger');
        }
    }

    // معالجة تسجيل الخروج
    handleLogout() {
        this.showConfirm('هل أنت متأكد من تسجيل الخروج؟', () => {
            this.isLoggedIn = false;
            localStorage.setItem('isLoggedIn', 'false');
            // مسح حالة التحقق من الإعدادات
            sessionStorage.removeItem('settingsUnlocked');
            this.showLoginScreen();
            this.showAlert('تم تسجيل الخروج بنجاح', 'info');
        });
    }

    // معالجة التنقل
    handleNavigation(e) {
        e.preventDefault();
        const page = e.currentTarget.dataset.page;
        this.navigateToPage(page);
    }

    // التنقل إلى صفحة
    navigateToPage(page) {
        // إزالة الفئة النشطة من جميع الروابط
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });

        // إضافة الفئة النشطة للرابط الحالي
        const activeLink = document.querySelector(`[data-page="${page}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }

        // تحديث عنوان الصفحة
        this.updatePageTitle(page);

        // تحميل محتوى الصفحة
        this.loadPageContent(page);

        // إغلاق القائمة الجانبية في الشاشات الصغيرة
        if (window.innerWidth <= 768) {
            document.getElementById('sidebar').classList.remove('show');
        }

        this.currentPage = page;
    }

    // تحديث عنوان الصفحة
    updatePageTitle(page) {
        const titles = {
            dashboard: 'لوحة المعلومات',
            sales: 'المبيعات',
            products: 'المنتجات',
            customers: 'العملاء',
            suppliers: 'الموردين',
            purchases: 'المشتريات',
            debts: 'الديون والمدفوعات',
            reports: 'التقارير',
            settings: 'الإعدادات'
        };

        const pageTitle = document.getElementById('pageTitle');
        if (pageTitle) {
            pageTitle.textContent = titles[page] || 'الصفحة';
        }
    }

    // تحميل محتوى الصفحة
    loadPageContent(page) {
        const pageContent = document.getElementById('pageContent');
        if (!pageContent) return;

        // مسح المحتوى السابق
        pageContent.innerHTML = '';

        // تحميل المحتوى حسب الصفحة
        switch (page) {
            case 'dashboard':
                if (typeof loadDashboard === 'function') {
                    loadDashboard();
                }
                break;
            case 'products':
                if (typeof loadProducts === 'function') {
                    loadProducts();
                }
                break;
            case 'sales':
                if (typeof loadSales === 'function') {
                    loadSales();
                }
                break;
            case 'customers':
                if (typeof loadCustomers === 'function') {
                    loadCustomers();
                }
                break;
            case 'suppliers':
                if (typeof loadSuppliers === 'function') {
                    loadSuppliers();
                }
                break;
            case 'purchases':
                if (typeof loadPurchases === 'function') {
                    loadPurchases();
                }
                break;
            case 'debts':
                if (typeof loadDebts === 'function') {
                    loadDebts();
                }
                break;
            case 'debts':
                if (typeof loadDebts === 'function') {
                    loadDebts();
                }
                break;
            case 'reports':
                if (typeof loadReports === 'function') {
                    loadReports();
                }
                break;
            case 'settings':
                // التحقق من حالة التحقق من الرقم السري في الجلسة الحالية
                if (sessionStorage.getItem('settingsUnlocked') === 'true') {
                    if (typeof loadSettings === 'function') {
                        loadSettings();
                    }
                } else {
                    this.showSettingsSecurityCheck(() => {
                        if (typeof loadSettings === 'function') {
                            loadSettings();
                        }
                    });
                }
                break;
            default:
                pageContent.innerHTML = '<div class="card"><h3>الصفحة غير موجودة</h3></div>';
        }
    }

    // عرض شاشة تسجيل الدخول
    showLoginScreen() {
        document.getElementById('loginScreen').style.display = 'flex';
        document.getElementById('mainApp').style.display = 'none';
        document.getElementById('password').value = '';
        document.getElementById('password').focus();
    }

    // عرض التطبيق الرئيسي
    showMainApp() {
        document.getElementById('loginScreen').style.display = 'none';
        document.getElementById('mainApp').style.display = 'grid';
        this.navigateToPage('dashboard');
    }

    // تبديل القائمة الجانبية
    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('show');
    }

    // عرض نافذة التأكيد
    showConfirm(message, onConfirm, title = 'تأكيد العملية') {
        const modal = document.getElementById('confirmModal');
        const titleElement = document.getElementById('confirmTitle');
        const messageElement = document.getElementById('confirmMessage');
        const yesButton = document.getElementById('confirmYes');

        titleElement.textContent = title;
        messageElement.textContent = message;

        // إزالة مستمعي الأحداث السابقين
        const newYesButton = yesButton.cloneNode(true);
        yesButton.parentNode.replaceChild(newYesButton, yesButton);

        // إضافة مستمع الحدث الجديد
        newYesButton.addEventListener('click', () => {
            this.closeAllModals();
            if (onConfirm) onConfirm();
        });

        modal.classList.add('show');
    }

    // عرض نافذة التنبيه
    showAlert(message, type = 'info', title = 'تنبيه') {
        const modal = document.getElementById('alertModal');
        const titleElement = document.getElementById('alertTitle');
        const messageElement = document.getElementById('alertMessage');

        titleElement.textContent = title;
        messageElement.textContent = message;

        // تغيير لون النافذة حسب النوع
        const modalContent = modal.querySelector('.modal-content');
        modalContent.className = 'modal-content';
        modalContent.classList.add(`alert-${type}`);

        modal.classList.add('show');

        // إغلاق تلقائي بعد 3 ثوان للرسائل الناجحة
        if (type === 'success') {
            setTimeout(() => {
                this.closeAllModals();
            }, 3000);
        }
    }

    // إغلاق جميع النوافذ المنبثقة
    closeAllModals() {
        document.querySelectorAll('.modal').forEach(modal => {
            modal.classList.remove('show');
        });
    }

    // عرض نافذة التحقق من الرقم السري للإعدادات
    showSettingsSecurityCheck(onSuccess) {
        const content = `
            <div class="security-check">
                <div class="security-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h3>منطقة محمية</h3>
                <p>يرجى إدخال الرقم السري للوصول إلى الإعدادات</p>
                <form id="securityForm" class="security-form">
                    <div class="form-group">
                        <input type="password" id="securityCode" placeholder="أدخل الرقم السري"
                               maxlength="7" autocomplete="off" required>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-unlock"></i> تأكيد
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="app.closeAllModals()">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </form>
            </div>

            <style>
                .security-check {
                    text-align: center;
                    padding: 2rem;
                }
                .security-icon {
                    font-size: 4rem;
                    color: var(--warning-color);
                    margin-bottom: 1rem;
                }
                .security-check h3 {
                    color: var(--text-primary);
                    margin-bottom: 0.5rem;
                }
                .security-check p {
                    color: var(--text-secondary);
                    margin-bottom: 2rem;
                }
                .security-form .form-group {
                    margin-bottom: 1.5rem;
                }
                .security-form input {
                    width: 100%;
                    padding: 1rem;
                    border: 2px solid #e0e0e0;
                    border-radius: var(--border-radius);
                    font-size: 1.2rem;
                    text-align: center;
                    letter-spacing: 0.2em;
                    font-family: monospace;
                    background: var(--bg-secondary);
                    transition: all 0.3s ease;
                }
                .security-form input:focus {
                    outline: none;
                    border-color: var(--primary-color);
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }
                .form-actions {
                    display: flex;
                    gap: 1rem;
                    justify-content: center;
                }
                .form-actions .btn {
                    min-width: 120px;
                }
            </style>
        `;

        // إنشاء النافذة المنبثقة
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content" style="max-width: 400px;">
                ${content}
            </div>
        `;

        document.body.appendChild(modal);

        // إظهار النافذة
        setTimeout(() => modal.classList.add('show'), 10);

        // التركيز على حقل الإدخال
        setTimeout(() => {
            const input = document.getElementById('securityCode');
            if (input) input.focus();
        }, 100);

        // معالجة النموذج
        const form = modal.querySelector('#securityForm');
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            const enteredCode = document.getElementById('securityCode').value;

            if (enteredCode === '5016178') {
                // الرقم السري صحيح - حفظ حالة التحقق في الجلسة
                sessionStorage.setItem('settingsUnlocked', 'true');
                modal.classList.remove('show');
                setTimeout(() => modal.remove(), 300);
                this.showAlert('تم التحقق بنجاح - تم فتح الإعدادات', 'success');
                if (onSuccess) onSuccess();
            } else {
                // الرقم السري خاطئ
                this.showAlert('الرقم السري غير صحيح', 'danger');
                document.getElementById('securityCode').value = '';
                document.getElementById('securityCode').focus();

                // إضافة تأثير اهتزاز للحقل
                const input = document.getElementById('securityCode');
                input.style.animation = 'shake 0.5s ease-in-out';
                setTimeout(() => {
                    input.style.animation = '';
                }, 500);
            }
        });

        // إضافة تأثير الاهتزاز
        if (!document.getElementById('shakeAnimation')) {
            const style = document.createElement('style');
            style.id = 'shakeAnimation';
            style.textContent = `
                @keyframes shake {
                    0%, 100% { transform: translateX(0); }
                    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
                    20%, 40%, 60%, 80% { transform: translateX(5px); }
                }
            `;
            document.head.appendChild(style);
        }

        // إغلاق النافذة عند النقر خارجها
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.classList.remove('show');
                setTimeout(() => modal.remove(), 300);
            }
        });
    }

    // تنسيق العملة
    formatCurrency(amount) {
        return db.formatCurrency(amount);
    }

    // تنسيق التاريخ
    formatDate(date) {
        return db.formatDate(date);
    }

    // تنسيق التاريخ والوقت
    formatDateTime(date) {
        return db.formatDateTime(date);
    }

    // تحويل إلى أرقام عربية
    toArabicNumbers(num) {
        return db.toArabicNumbers(num);
    }

    // إنشاء عنصر HTML
    createElement(tag, className = '', innerHTML = '') {
        const element = document.createElement(tag);
        if (className) element.className = className;
        if (innerHTML) element.innerHTML = innerHTML;
        return element;
    }

    // إنشاء جدول
    createTable(headers, data, actions = []) {
        const table = this.createElement('table', 'table');
        
        // إنشاء رأس الجدول
        const thead = this.createElement('thead');
        const headerRow = this.createElement('tr');
        
        headers.forEach(header => {
            const th = this.createElement('th', '', header);
            headerRow.appendChild(th);
        });
        
        if (actions.length > 0) {
            const actionTh = this.createElement('th', '', 'الإجراءات');
            headerRow.appendChild(actionTh);
        }
        
        thead.appendChild(headerRow);
        table.appendChild(thead);
        
        // إنشاء جسم الجدول
        const tbody = this.createElement('tbody');
        
        data.forEach(row => {
            const tr = this.createElement('tr');
            
            row.forEach(cell => {
                const td = this.createElement('td', '', cell);
                tr.appendChild(td);
            });
            
            if (actions.length > 0) {
                const actionTd = this.createElement('td');
                actions.forEach(action => {
                    const button = this.createElement('button', `btn btn-${action.type}`, action.text);
                    button.addEventListener('click', action.handler);
                    actionTd.appendChild(button);
                });
                tr.appendChild(actionTd);
            }
            
            tbody.appendChild(tr);
        });
        
        table.appendChild(tbody);
        return table;
    }

    // طباعة المحتوى
    printContent(content, title = 'طباعة') {
        const printWindow = window.open('', '_blank');
        const settings = db.getSettings();
        
        printWindow.document.write(`
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>${title}</title>
                <style>
                    body { font-family: 'Cairo', Arial, sans-serif; direction: rtl; }
                    .print-header { text-align: center; margin-bottom: 20px; border-bottom: 2px solid #333; padding-bottom: 10px; }
                    .company-info { margin-bottom: 20px; }
                    table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                    th { background-color: #f5f5f5; }
                    .total { font-weight: bold; font-size: 1.2em; }
                    @media print { body { margin: 0; } }
                </style>
            </head>
            <body>
                <div class="print-header">
                    <h1>${settings.companyName}</h1>
                    <p>${settings.companyAddress} | ${settings.companyPhone} | ${settings.companyEmail}</p>
                </div>
                ${content}
                <div style="margin-top: 30px; text-align: center; font-size: 0.9em; color: #666;">
                    تم الطباعة في: ${this.formatDateTime(new Date())}
                </div>
            </body>
            </html>
        `);
        
        printWindow.document.close();
        printWindow.focus();
        printWindow.print();
    }
}

// تهيئة التطبيق عند تحميل الصفحة
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new App();
});
