// نظام التقارير والإحصائيات
async function loadReports() {
    const pageContent = document.getElementById('pageContent');

    // إنشاء شبكة التقارير
    const reportsGrid = createReportsGrid();
    pageContent.appendChild(reportsGrid);

    // إنشاء قسم الفلاتر
    const filtersSection = await createFiltersSection();
    pageContent.appendChild(filtersSection);

    // إنشاء قسم النتائج
    const resultsSection = createResultsSection();
    pageContent.appendChild(resultsSection);
}

// إنشاء شبكة التقارير
function createReportsGrid() {
    const reportsData = [
        {
            title: 'تقرير المبيعات',
            description: 'تقرير شامل عن المبيعات والإيرادات',
            icon: 'fas fa-chart-line',
            color: 'success',
            action: () => generateSalesReport()
        },
        {
            title: 'تقرير المخزون',
            description: 'حالة المخزون والمنتجات',
            icon: 'fas fa-boxes',
            color: 'info',
            action: () => generateInventoryReport()
        },
        {
            title: 'تقرير العملاء',
            description: 'إحصائيات العملاء والديون',
            icon: 'fas fa-users',
            color: 'warning',
            action: () => generateCustomersReport()
        },
        {
            title: 'تقرير المشتريات',
            description: 'تقرير المشتريات والموردين',
            icon: 'fas fa-shopping-bag',
            color: 'primary',
            action: () => generatePurchasesReport()
        },
        {
            title: 'تقرير الأرباح',
            description: 'تحليل الأرباح والخسائر',
            icon: 'fas fa-money-bill-trend-up',
            color: 'success',
            action: () => generateProfitReport()
        },
        {
            title: 'تقرير مخصص',
            description: 'إنشاء تقرير مخصص حسب الحاجة',
            icon: 'fas fa-cog',
            color: 'secondary',
            action: () => showCustomReportBuilder()
        }
    ];

    const grid = document.createElement('div');
    grid.className = 'reports-grid';
    grid.style.display = 'grid';
    grid.style.gridTemplateColumns = 'repeat(auto-fit, minmax(300px, 1fr))';
    grid.style.gap = '1.5rem';
    grid.style.marginBottom = '2rem';

    reportsData.forEach(report => {
        const card = document.createElement('div');
        card.className = 'report-card';
        card.style.cursor = 'pointer';
        card.addEventListener('click', report.action);

        card.innerHTML = `
            <div class="report-icon ${report.color}">
                <i class="${report.icon}"></i>
            </div>
            <div class="report-content">
                <h3>${report.title}</h3>
                <p>${report.description}</p>
            </div>
            <div class="report-arrow">
                <i class="fas fa-chevron-left"></i>
            </div>
        `;

        grid.appendChild(card);
    });

    // إضافة الأنماط
    const style = document.createElement('style');
    style.textContent = `
        .report-card {
            background: var(--bg-secondary);
            border-radius: var(--border-radius);
            box-shadow: var(--neu-shadow);
            padding: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        .report-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-heavy);
            border-color: var(--primary-color);
        }
        .report-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }
        .report-icon.success { background: var(--success-color); }
        .report-icon.info { background: var(--info-color); }
        .report-icon.warning { background: var(--warning-color); }
        .report-icon.primary { background: var(--primary-color); }
        .report-icon.secondary { background: var(--text-secondary); }
        .report-content {
            flex: 1;
        }
        .report-content h3 {
            margin: 0 0 0.5rem 0;
            color: var(--text-primary);
        }
        .report-content p {
            margin: 0;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }
        .report-arrow {
            color: var(--text-secondary);
            font-size: 1.2rem;
        }
    `;
    document.head.appendChild(style);

    return grid;
}

// إنشاء قسم الفلاتر
async function createFiltersSection() {
    const section = document.createElement('div');
    section.className = 'card';
    section.style.marginBottom = '1.5rem';
    section.style.display = 'none';
    section.id = 'filtersSection';

    section.innerHTML = `
        <div class="card-header">
            <h3 class="card-title">فلاتر التقرير</h3>
            <button class="btn btn-secondary" onclick="hideFilters()">
                <i class="fas fa-times"></i> إغلاق
            </button>
        </div>
        <div class="filters-content">
            <div class="form-row">
                <div class="form-group">
                    <label>من تاريخ</label>
                    <input type="date" id="startDate" value="${new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0]}">
                </div>
                <div class="form-group">
                    <label>إلى تاريخ</label>
                    <input type="date" id="endDate" value="${new Date().toISOString().split('T')[0]}">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>العميل</label>
                    <select id="customerFilter">
                        <option value="">جميع العملاء</option>
                        ${(await db.getCustomers()).map(c => `<option value="${c.id}">${c.name}</option>`).join('')}
                    </select>
                </div>
                <div class="form-group">
                    <label>المنتج</label>
                    <select id="productFilter">
                        <option value="">جميع المنتجات</option>
                        ${(await db.getProducts()).map(p => `<option value="${p.id}">${p.name}</option>`).join('')}
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>طريقة الدفع</label>
                    <select id="paymentMethodFilter">
                        <option value="">جميع طرق الدفع</option>
                        <option value="cash">نقداً</option>
                        <option value="credit">آجل</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>نوع التقرير</label>
                    <select id="reportType">
                        <option value="summary">ملخص</option>
                        <option value="detailed">تفصيلي</option>
                    </select>
                </div>
            </div>
            <div class="filters-actions">
                <button class="btn btn-primary" onclick="applyFiltersAndGenerate()">
                    <i class="fas fa-filter"></i> تطبيق الفلاتر وإنشاء التقرير
                </button>
                <button class="btn btn-secondary" onclick="resetFilters()">
                    <i class="fas fa-undo"></i> إعادة تعيين
                </button>
            </div>
        </div>
    `;

    return section;
}

// إنشاء قسم النتائج
function createResultsSection() {
    const section = document.createElement('div');
    section.className = 'card';
    section.style.display = 'none';
    section.id = 'resultsSection';

    section.innerHTML = `
        <div class="card-header">
            <h3 class="card-title" id="reportTitle">نتائج التقرير</h3>
            <div>
                <button class="btn btn-success" onclick="exportCurrentReport()">
                    <i class="fas fa-download"></i> تصدير
                </button>
                <button class="btn btn-info" onclick="printCurrentReport()">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <button class="btn btn-secondary" onclick="hideResults()">
                    <i class="fas fa-times"></i> إغلاق
                </button>
            </div>
        </div>
        <div id="reportContent" class="report-content">
            <!-- سيتم عرض محتوى التقرير هنا -->
        </div>
    `;

    return section;
}

// إنشاء تقرير المبيعات
function generateSalesReport() {
    showFilters('تقرير المبيعات');
    window.currentReportType = 'sales';
}

// إنشاء تقرير المخزون
async function generateInventoryReport() {
    const products = await db.getProducts();
    const settings = await db.getSettings();

    const lowStockProducts = products.filter(p => p.quantity <= settings.lowStockAlert);
    const outOfStockProducts = products.filter(p => p.quantity === 0);
    const totalValue = products.reduce((sum, p) => sum + (p.price * p.quantity), 0);

    const content = `
        <div class="report-summary">
            <div class="summary-cards">
                <div class="summary-card">
                    <h4>${db.toArabicNumbers(products.length)}</h4>
                    <p>إجمالي المنتجات</p>
                </div>
                <div class="summary-card warning">
                    <h4>${db.toArabicNumbers(lowStockProducts.length)}</h4>
                    <p>منتجات منخفضة المخزون</p>
                </div>
                <div class="summary-card danger">
                    <h4>${db.toArabicNumbers(outOfStockProducts.length)}</h4>
                    <p>منتجات نفد مخزونها</p>
                </div>
                <div class="summary-card success">
                    <h4>${db.formatCurrencySync(totalValue, settings?.currency)}</h4>
                    <p>قيمة المخزون الإجمالية</p>
                </div>
            </div>
        </div>

        <div class="report-table">
            <h4>تفاصيل المخزون</h4>
            <table class="table">
                <thead>
                    <tr>
                        <th>اسم المنتج</th>
                        <th>الفئة</th>
                        <th>الكمية</th>
                        <th>السعر</th>
                        <th>القيمة الإجمالية</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    ${products.map(product => `
                        <tr>
                            <td>${product.name}</td>
                            <td>${product.category || 'غير محدد'}</td>
                            <td>${db.toArabicNumbers(product.quantity)}</td>
                            <td>${db.formatCurrencySync(product.price, settings?.currency)}</td>
                            <td>${db.formatCurrencySync(product.price * product.quantity, settings?.currency)}</td>
                            <td>
                                <span class="badge ${product.quantity === 0 ? 'badge-danger' : product.quantity <= settings.lowStockAlert ? 'badge-warning' : 'badge-success'}">
                                    ${product.quantity === 0 ? 'نفد المخزون' : product.quantity <= settings.lowStockAlert ? 'مخزون منخفض' : 'متوفر'}
                                </span>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;

    showReport('تقرير المخزون', content);
}

// إنشاء تقرير العملاء
async function generateCustomersReport() {
    const customers = (await db.getCustomers()).filter(c => c.id !== 'guest');
    const sales = await db.getSales();

    const customersWithStats = customers.map(customer => {
        const customerSales = sales.filter(s => s.customerId === customer.id);
        const totalPurchases = customerSales.reduce((sum, s) => sum + s.total, 0);
        const lastPurchase = customerSales.length > 0 ? customerSales[customerSales.length - 1] : null;

        return {
            ...customer,
            totalPurchases,
            salesCount: customerSales.length,
            lastPurchase: lastPurchase ? lastPurchase.createdAt : null
        };
    });

    // ترتيب العملاء حسب إجمالي المشتريات
    customersWithStats.sort((a, b) => b.totalPurchases - a.totalPurchases);

    const totalCustomers = customers.length;
    const activeCustomers = customersWithStats.filter(c => c.salesCount > 0).length;
    const totalRevenue = customersWithStats.reduce((sum, c) => sum + c.totalPurchases, 0);
    const totalDebts = customers.reduce((sum, c) => sum + (c.balance > 0 ? c.balance : 0), 0);

    const content = `
        <div class="report-summary">
            <div class="summary-cards">
                <div class="summary-card">
                    <h4>${db.toArabicNumbers(totalCustomers)}</h4>
                    <p>إجمالي العملاء</p>
                </div>
                <div class="summary-card success">
                    <h4>${db.toArabicNumbers(activeCustomers)}</h4>
                    <p>عملاء نشطين</p>
                </div>
                <div class="summary-card info">
                    <h4>${db.formatCurrencySync(totalRevenue)}</h4>
                    <p>إجمالي الإيرادات</p>
                </div>
                <div class="summary-card warning">
                    <h4>${db.formatCurrencySync(totalDebts)}</h4>
                    <p>إجمالي الديون</p>
                </div>
            </div>
        </div>

        <div class="report-table">
            <h4>تفاصيل العملاء</h4>
            <table class="table">
                <thead>
                    <tr>
                        <th>اسم العميل</th>
                        <th>الهاتف</th>
                        <th>عدد المشتريات</th>
                        <th>إجمالي المشتريات</th>
                        <th>الرصيد</th>
                        <th>آخر مشترى</th>
                    </tr>
                </thead>
                <tbody>
                    ${customersWithStats.map(customer => `
                        <tr>
                            <td>${customer.name}</td>
                            <td>${customer.phone || '-'}</td>
                            <td>${db.toArabicNumbers(customer.salesCount)}</td>
                            <td>${db.formatCurrencySync(customer.totalPurchases)}</td>
                            <td>
                                <span class="badge ${customer.balance > 0 ? 'badge-danger' : customer.balance < 0 ? 'badge-success' : 'badge-secondary'}">
                                    ${db.formatCurrencySync(Math.abs(customer.balance))} ${customer.balance > 0 ? 'مدين' : customer.balance < 0 ? 'دائن' : 'متوازن'}
                                </span>
                            </td>
                            <td>${customer.lastPurchase ? db.formatDate(customer.lastPurchase) : 'لا توجد مشتريات'}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;

    showReport('تقرير العملاء', content);
}

// إنشاء تقرير المشتريات
async function generatePurchasesReport() {
    const purchases = await db.getPurchases();
    const suppliers = await db.getSuppliers();

    const totalPurchases = purchases.reduce((sum, p) => sum + p.total, 0);
    const totalItems = purchases.reduce((sum, p) => sum + p.items.length, 0);
    const averagePurchase = purchases.length > 0 ? totalPurchases / purchases.length : 0;

    // إحصائيات الموردين
    const supplierStats = suppliers.map(supplier => {
        const supplierPurchases = purchases.filter(p => p.supplierId === supplier.id);
        const totalAmount = supplierPurchases.reduce((sum, p) => sum + p.total, 0);

        return {
            name: supplier.name,
            purchasesCount: supplierPurchases.length,
            totalAmount
        };
    }).sort((a, b) => b.totalAmount - a.totalAmount);

    const content = `
        <div class="report-summary">
            <div class="summary-cards">
                <div class="summary-card">
                    <h4>${db.toArabicNumbers(purchases.length)}</h4>
                    <p>إجمالي المشتريات</p>
                </div>
                <div class="summary-card info">
                    <h4>${db.formatCurrencySync(totalPurchases)}</h4>
                    <p>إجمالي المبلغ</p>
                </div>
                <div class="summary-card success">
                    <h4>${db.toArabicNumbers(totalItems)}</h4>
                    <p>إجمالي الأصناف</p>
                </div>
                <div class="summary-card warning">
                    <h4>${db.formatCurrencySync(averagePurchase)}</h4>
                    <p>متوسط المشترى</p>
                </div>
            </div>
        </div>

        <div class="report-table">
            <h4>إحصائيات الموردين</h4>
            <table class="table">
                <thead>
                    <tr>
                        <th>اسم المورد</th>
                        <th>عدد المشتريات</th>
                        <th>إجمالي المبلغ</th>
                        <th>النسبة المئوية</th>
                    </tr>
                </thead>
                <tbody>
                    ${supplierStats.map(supplier => `
                        <tr>
                            <td>${supplier.name}</td>
                            <td>${db.toArabicNumbers(supplier.purchasesCount)}</td>
                            <td>${db.formatCurrencySync(supplier.totalAmount)}</td>
                            <td>${AppHelpers.calculatePercentage(supplier.totalAmount, totalPurchases)}%</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>

        <div class="report-table">
            <h4>تفاصيل المشتريات</h4>
            <table class="table">
                <thead>
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>المورد</th>
                        <th>عدد الأصناف</th>
                        <th>المبلغ</th>
                        <th>التاريخ</th>
                    </tr>
                </thead>
                <tbody>
                    ${purchases.slice(-20).reverse().map(purchase => {
                        const supplier = suppliers.find(s => s.id === purchase.supplierId);
                        return `
                            <tr>
                                <td>${db.toArabicNumbers(purchase.invoiceNumber)}</td>
                                <td>${supplier ? supplier.name : 'مورد محذوف'}</td>
                                <td>${db.toArabicNumbers(purchase.items.length)}</td>
                                <td>${db.formatCurrencySync(purchase.total)}</td>
                                <td>${db.formatDate(purchase.createdAt)}</td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        </div>
    `;

    showReport('تقرير المشتريات', content);
}

// إنشاء تقرير الأرباح
async function generateProfitReport() {
    const sales = await db.getSales();
    const purchases = await db.getPurchases();

    // حساب الإيرادات
    const totalRevenue = sales.reduce((sum, s) => sum + s.total, 0);
    const totalCosts = purchases.reduce((sum, p) => sum + p.total, 0);
    const grossProfit = totalRevenue - totalCosts;
    const profitMargin = totalRevenue > 0 ? (grossProfit / totalRevenue) * 100 : 0;

    // تحليل شهري
    const monthlyData = {};
    const currentYear = new Date().getFullYear();

    // تجميع البيانات الشهرية
    for (let month = 0; month < 12; month++) {
        const monthKey = `${currentYear}-${(month + 1).toString().padStart(2, '0')}`;
        monthlyData[monthKey] = {
            revenue: 0,
            costs: 0,
            profit: 0
        };
    }

    sales.forEach(sale => {
        const saleDate = new Date(sale.createdAt);
        if (saleDate.getFullYear() === currentYear) {
            const monthKey = `${currentYear}-${(saleDate.getMonth() + 1).toString().padStart(2, '0')}`;
            if (monthlyData[monthKey]) {
                monthlyData[monthKey].revenue += sale.total;
            }
        }
    });

    purchases.forEach(purchase => {
        const purchaseDate = new Date(purchase.createdAt);
        if (purchaseDate.getFullYear() === currentYear) {
            const monthKey = `${currentYear}-${(purchaseDate.getMonth() + 1).toString().padStart(2, '0')}`;
            if (monthlyData[monthKey]) {
                monthlyData[monthKey].costs += purchase.total;
            }
        }
    });

    // حساب الأرباح الشهرية
    Object.keys(monthlyData).forEach(month => {
        monthlyData[month].profit = monthlyData[month].revenue - monthlyData[month].costs;
    });

    const monthNames = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    const content = `
        <div class="report-summary">
            <div class="summary-cards">
                <div class="summary-card success">
                    <h4>${db.formatCurrencySync(totalRevenue)}</h4>
                    <p>إجمالي الإيرادات</p>
                </div>
                <div class="summary-card danger">
                    <h4>${db.formatCurrencySync(totalCosts)}</h4>
                    <p>إجمالي التكاليف</p>
                </div>
                <div class="summary-card ${grossProfit >= 0 ? 'info' : 'warning'}">
                    <h4>${db.formatCurrencySync(Math.abs(grossProfit))}</h4>
                    <p>${grossProfit >= 0 ? 'إجمالي الربح' : 'إجمالي الخسارة'}</p>
                </div>
                <div class="summary-card primary">
                    <h4>${db.toArabicNumbers(profitMargin.toFixed(1))}%</h4>
                    <p>هامش الربح</p>
                </div>
            </div>
        </div>

        <div class="report-table">
            <h4>التحليل الشهري لعام ${db.toArabicNumbers(currentYear)}</h4>
            <table class="table">
                <thead>
                    <tr>
                        <th>الشهر</th>
                        <th>الإيرادات</th>
                        <th>التكاليف</th>
                        <th>الربح/الخسارة</th>
                        <th>هامش الربح</th>
                    </tr>
                </thead>
                <tbody>
                    ${Object.keys(monthlyData).map((monthKey, index) => {
                        const data = monthlyData[monthKey];
                        const margin = data.revenue > 0 ? (data.profit / data.revenue) * 100 : 0;
                        return `
                            <tr>
                                <td>${monthNames[index]}</td>
                                <td>${db.formatCurrencySync(data.revenue)}</td>
                                <td>${db.formatCurrencySync(data.costs)}</td>
                                <td>
                                    <span class="badge ${data.profit >= 0 ? 'badge-success' : 'badge-danger'}">
                                        ${db.formatCurrencySync(Math.abs(data.profit))} ${data.profit >= 0 ? 'ربح' : 'خسارة'}
                                    </span>
                                </td>
                                <td>${db.toArabicNumbers(margin.toFixed(1))}%</td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        </div>
    `;

    showReport('تقرير الأرباح والخسائر', content);
}

// عرض الفلاتر
function showFilters(reportTitle) {
    document.getElementById('filtersSection').style.display = 'block';
    document.querySelector('#filtersSection .card-title').textContent = `فلاتر ${reportTitle}`;
}

// إخفاء الفلاتر
function hideFilters() {
    document.getElementById('filtersSection').style.display = 'none';
}

// عرض التقرير
function showReport(title, content) {
    const resultsSection = document.getElementById('resultsSection');
    const reportTitle = document.getElementById('reportTitle');
    const reportContent = document.getElementById('reportContent');

    reportTitle.textContent = title;
    reportContent.innerHTML = content;
    resultsSection.style.display = 'block';

    // إضافة أنماط التقرير
    if (!document.getElementById('reportStyles')) {
        const style = document.createElement('style');
        style.id = 'reportStyles';
        style.textContent = `
            .report-summary {
                margin-bottom: 2rem;
            }
            .summary-cards {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 1rem;
                margin-bottom: 1rem;
            }
            .summary-card {
                background: #f8f9fa;
                padding: 1.5rem;
                border-radius: 8px;
                text-align: center;
                border-right: 4px solid var(--primary-color);
            }
            .summary-card.success { border-right-color: var(--success-color); }
            .summary-card.danger { border-right-color: var(--danger-color); }
            .summary-card.warning { border-right-color: var(--warning-color); }
            .summary-card.info { border-right-color: var(--info-color); }
            .summary-card h4 {
                margin: 0 0 0.5rem 0;
                font-size: 1.5rem;
                font-weight: bold;
                color: var(--text-primary);
            }
            .summary-card p {
                margin: 0;
                color: var(--text-secondary);
                font-size: 0.9rem;
            }
            .report-table {
                margin-bottom: 2rem;
            }
            .report-table h4 {
                margin-bottom: 1rem;
                color: var(--primary-color);
                border-bottom: 2px solid var(--primary-color);
                padding-bottom: 0.5rem;
            }
        `;
        document.head.appendChild(style);
    }

    // حفظ التقرير الحالي للتصدير والطباعة
    window.currentReport = {
        title: title,
        content: content
    };
}

// إخفاء النتائج
function hideResults() {
    document.getElementById('resultsSection').style.display = 'none';
}

// تطبيق الفلاتر وإنشاء التقرير
async function applyFiltersAndGenerate() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const customerId = document.getElementById('customerFilter').value;
    const productId = document.getElementById('productFilter').value;
    const paymentMethod = document.getElementById('paymentMethodFilter').value;
    const reportType = document.getElementById('reportType').value;

    if (window.currentReportType === 'sales') {
        await generateFilteredSalesReport(startDate, endDate, customerId, productId, paymentMethod, reportType);
    }

    hideFilters();
}

// إنشاء تقرير مبيعات مفلتر
async function generateFilteredSalesReport(startDate, endDate, customerId, productId, paymentMethod, reportType) {
    let sales = await db.getSales();

    // تطبيق الفلاتر
    if (startDate) {
        sales = sales.filter(s => new Date(s.createdAt) >= new Date(startDate));
    }
    if (endDate) {
        sales = sales.filter(s => new Date(s.createdAt) <= new Date(endDate + 'T23:59:59'));
    }
    if (customerId) {
        sales = sales.filter(s => s.customerId === customerId);
    }
    if (paymentMethod) {
        sales = sales.filter(s => s.paymentMethod === paymentMethod);
    }
    if (productId) {
        sales = sales.filter(s => s.items.some(item => item.productId === productId));
    }

    const totalSales = sales.reduce((sum, s) => sum + s.total, 0);
    const totalItems = sales.reduce((sum, s) => sum + s.items.reduce((itemSum, item) => itemSum + item.quantity, 0), 0);
    const averageSale = sales.length > 0 ? totalSales / sales.length : 0;
    const cashSales = sales.filter(s => s.paymentMethod === 'cash').reduce((sum, s) => sum + s.total, 0);
    const creditSales = sales.filter(s => s.paymentMethod === 'credit').reduce((sum, s) => sum + s.total, 0);

    let content = `
        <div class="report-summary">
            <div class="summary-cards">
                <div class="summary-card">
                    <h4>${db.toArabicNumbers(sales.length)}</h4>
                    <p>عدد المبيعات</p>
                </div>
                <div class="summary-card success">
                    <h4>${db.formatCurrencySync(totalSales)}</h4>
                    <p>إجمالي المبيعات</p>
                </div>
                <div class="summary-card info">
                    <h4>${db.toArabicNumbers(totalItems)}</h4>
                    <p>إجمالي الأصناف</p>
                </div>
                <div class="summary-card warning">
                    <h4>${db.formatCurrencySync(averageSale)}</h4>
                    <p>متوسط البيع</p>
                </div>
            </div>

            <div class="payment-breakdown" style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-top: 1rem;">
                <div class="summary-card success">
                    <h4>${db.formatCurrencySync(cashSales)}</h4>
                    <p>المبيعات النقدية</p>
                </div>
                <div class="summary-card warning">
                    <h4>${db.formatCurrencySync(creditSales)}</h4>
                    <p>المبيعات الآجلة</p>
                </div>
            </div>
        </div>
    `;

    if (reportType === 'detailed' && sales.length > 0) {
        content += `
            <div class="report-table">
                <h4>تفاصيل المبيعات</h4>
                <table class="table">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>المبلغ</th>
                            <th>طريقة الدفع</th>
                            <th>التاريخ</th>
                        </tr>
                    </thead>
                    <tbody id="salesReportTableBody">
                    </tbody>
                </table>
            </div>
        `;
    }

    showReport('تقرير المبيعات المفلتر', content);

    // ملء جدول المبيعات إذا كان موجوداً
    setTimeout(async () => {
        const tableBody = document.getElementById('salesReportTableBody');
        if (tableBody && sales.length > 0) {
            for (const sale of sales) {
                const customer = await db.getCustomerById(sale.customerId);
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${db.toArabicNumbers(sale.invoiceNumber || sale.id.slice(-6))}</td>
                    <td>${customer ? customer.name : 'عميل محذوف'}</td>
                    <td>${db.formatCurrencySync(sale.total)}</td>
                    <td>
                        <span class="badge ${sale.paymentMethod === 'cash' ? 'badge-success' : 'badge-warning'}">
                            ${sale.paymentMethod === 'cash' ? 'نقداً' : 'آجل'}
                        </span>
                    </td>
                    <td>${db.formatDateTime(sale.createdAt)}</td>
                `;
                tableBody.appendChild(row);
            }
        }
    }, 100);
}

// إعادة تعيين الفلاتر
function resetFilters() {
    document.getElementById('startDate').value = new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0];
    document.getElementById('endDate').value = new Date().toISOString().split('T')[0];
    document.getElementById('customerFilter').value = '';
    document.getElementById('productFilter').value = '';
    document.getElementById('paymentMethodFilter').value = '';
    document.getElementById('reportType').value = 'summary';
}

// تصدير التقرير الحالي
function exportCurrentReport() {
    if (!window.currentReport) {
        app.showAlert('لا يوجد تقرير لتصديره', 'warning');
        return;
    }

    const csvContent = convertReportToCSV(window.currentReport);
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `${window.currentReport.title}_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        app.showAlert('تم تصدير التقرير بنجاح', 'success');
    }
}

// طباعة التقرير الحالي
function printCurrentReport() {
    if (!window.currentReport) {
        app.showAlert('لا يوجد تقرير لطباعته', 'warning');
        return;
    }

    app.printContent(window.currentReport.content, window.currentReport.title);
}

// تحويل التقرير إلى CSV
function convertReportToCSV(report) {
    // استخراج البيانات من جداول HTML في التقرير
    const parser = new DOMParser();
    const doc = parser.parseFromString(report.content, 'text/html');
    const tables = doc.querySelectorAll('table');

    let csvContent = `${report.title}\n\n`;

    tables.forEach((table, index) => {
        const rows = table.querySelectorAll('tr');
        rows.forEach(row => {
            const cells = row.querySelectorAll('th, td');
            const rowData = Array.from(cells).map(cell => cell.textContent.trim());
            csvContent += rowData.join(',') + '\n';
        });
        csvContent += '\n';
    });

    return csvContent;
}

// عرض منشئ التقارير المخصصة
function showCustomReportBuilder() {
    app.showAlert('سيتم تطوير منشئ التقارير المخصصة قريباً', 'info');
}