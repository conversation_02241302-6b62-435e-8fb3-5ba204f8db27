<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأرقام العشرية - نظا<PERSON> BakryAfand POS</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .price-examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .price-example {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
            text-align: center;
        }
        .price-value {
            font-size: 1.2rem;
            font-weight: bold;
            color: #007bff;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار دعم الأرقام العشرية في الأسعار</h1>
        <p>هذه الصفحة لاختبار قبول الأرقام العشرية في جميع حقول الأسعار في النظام</p>
        
        <div class="test-section">
            <h3>اختبار إضافة منتج بسعر عشري</h3>
            <div class="form-group">
                <label>اسم المنتج:</label>
                <input type="text" id="productName" placeholder="اسم المنتج" value="منتج تجريبي">
            </div>
            <div class="form-group">
                <label>السعر (بالأرقام العشرية):</label>
                <input type="number" id="productPrice" step="0.01" min="0" placeholder="0.00" value="15.75">
            </div>
            <div class="form-group">
                <label>الكمية:</label>
                <input type="number" id="productQuantity" min="1" placeholder="1" value="10">
            </div>
            <button onclick="testAddProduct()">اختبار إضافة المنتج</button>
            <div id="product-test-results"></div>
        </div>
        
        <div class="test-section">
            <h3>اختبار حساب المجاميع مع الأرقام العشرية</h3>
            <div class="price-examples">
                <div class="price-example">
                    <div>السعر الأساسي</div>
                    <div class="price-value" id="basePrice">15.75</div>
                </div>
                <div class="price-example">
                    <div>الكمية</div>
                    <div class="price-value" id="quantity">3</div>
                </div>
                <div class="price-example">
                    <div>المجموع الفرعي</div>
                    <div class="price-value" id="subtotal">47.25</div>
                </div>
                <div class="price-example">
                    <div>الخصم</div>
                    <div class="price-value" id="discount">2.50</div>
                </div>
                <div class="price-example">
                    <div>الضريبة (5%)</div>
                    <div class="price-value" id="tax">2.24</div>
                </div>
                <div class="price-example">
                    <div>المجموع النهائي</div>
                    <div class="price-value" id="total">46.99</div>
                </div>
            </div>
            <button onclick="testCalculations()">اختبار الحسابات</button>
            <div id="calculation-results"></div>
        </div>
        
        <div class="test-section">
            <h3>اختبار تنسيق العملة</h3>
            <div class="form-group">
                <label>مبلغ للاختبار:</label>
                <input type="number" id="testAmount" step="0.01" min="0" placeholder="0.00" value="123.456">
            </div>
            <button onclick="testCurrencyFormatting()">اختبار التنسيق</button>
            <div id="formatting-results"></div>
        </div>
        
        <div class="test-section">
            <h3>اختبار حفظ واسترجاع البيانات</h3>
            <button onclick="testDataPersistence()">اختبار حفظ البيانات</button>
            <div id="persistence-results"></div>
        </div>
        
        <div class="test-section">
            <h3>العودة للنظام</h3>
            <button onclick="window.location.href='index.html'">العودة للنظام الرئيسي</button>
        </div>
    </div>

    <script src="database.js"></script>
    <script>
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            container.appendChild(div);
        }
        
        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }
        
        async function testAddProduct() {
            clearResults('product-test-results');
            addResult('product-test-results', 'اختبار إضافة منتج بسعر عشري...', 'info');
            
            try {
                const name = document.getElementById('productName').value;
                const price = parseFloat(document.getElementById('productPrice').value);
                const quantity = parseInt(document.getElementById('productQuantity').value);
                
                if (!name || isNaN(price) || isNaN(quantity)) {
                    addResult('product-test-results', '✗ يرجى ملء جميع الحقول بقيم صحيحة', 'error');
                    return;
                }
                
                const productData = {
                    name: name,
                    description: 'منتج تجريبي للاختبار',
                    category: 'اختبار',
                    price: price,
                    quantity: quantity,
                    barcode: ''
                };
                
                await db.saveProduct(productData);
                
                addResult('product-test-results', `✓ تم إضافة المنتج بنجاح`, 'success');
                addResult('product-test-results', `الاسم: ${name}`, 'info');
                addResult('product-test-results', `السعر: ${price.toFixed(2)} ر.ع`, 'info');
                addResult('product-test-results', `الكمية: ${quantity}`, 'info');
                
                // التحقق من حفظ السعر بشكل صحيح
                const products = await db.getProducts();
                const savedProduct = products.find(p => p.name === name);
                
                if (savedProduct && savedProduct.price === price) {
                    addResult('product-test-results', '✓ تم حفظ السعر العشري بشكل صحيح', 'success');
                } else {
                    addResult('product-test-results', '✗ لم يتم حفظ السعر العشري بشكل صحيح', 'error');
                }
                
            } catch (error) {
                addResult('product-test-results', `✗ خطأ في إضافة المنتج: ${error.message}`, 'error');
            }
        }
        
        function testCalculations() {
            clearResults('calculation-results');
            addResult('calculation-results', 'اختبار الحسابات مع الأرقام العشرية...', 'info');
            
            try {
                const basePrice = 15.75;
                const quantity = 3;
                const discount = 2.50;
                const taxRate = 5; // 5%
                
                const subtotal = basePrice * quantity;
                const afterDiscount = subtotal - discount;
                const taxAmount = afterDiscount * (taxRate / 100);
                const total = afterDiscount + taxAmount;
                
                // تحديث العرض
                document.getElementById('basePrice').textContent = basePrice.toFixed(2);
                document.getElementById('quantity').textContent = quantity;
                document.getElementById('subtotal').textContent = subtotal.toFixed(2);
                document.getElementById('discount').textContent = discount.toFixed(2);
                document.getElementById('tax').textContent = taxAmount.toFixed(2);
                document.getElementById('total').textContent = total.toFixed(2);
                
                addResult('calculation-results', '✓ تم حساب جميع القيم بنجاح', 'success');
                addResult('calculation-results', `المجموع الفرعي: ${subtotal.toFixed(2)}`, 'info');
                addResult('calculation-results', `بعد الخصم: ${afterDiscount.toFixed(2)}`, 'info');
                addResult('calculation-results', `الضريبة: ${taxAmount.toFixed(2)}`, 'info');
                addResult('calculation-results', `المجموع النهائي: ${total.toFixed(2)}`, 'info');
                
                // التحقق من دقة الحسابات
                const expectedTotal = 46.99;
                if (Math.abs(total - expectedTotal) < 0.01) {
                    addResult('calculation-results', '✓ الحسابات دقيقة', 'success');
                } else {
                    addResult('calculation-results', `⚠️ قد تكون هناك اختلافات طفيفة في التقريب`, 'info');
                }
                
            } catch (error) {
                addResult('calculation-results', `✗ خطأ في الحسابات: ${error.message}`, 'error');
            }
        }
        
        function testCurrencyFormatting() {
            clearResults('formatting-results');
            addResult('formatting-results', 'اختبار تنسيق العملة...', 'info');
            
            try {
                const amount = parseFloat(document.getElementById('testAmount').value);
                
                if (isNaN(amount)) {
                    addResult('formatting-results', '✗ يرجى إدخال مبلغ صحيح', 'error');
                    return;
                }
                
                // اختبار تنسيق العملة
                const formatted = db.formatCurrencySync(amount);
                
                addResult('formatting-results', `✓ تم تنسيق المبلغ بنجاح`, 'success');
                addResult('formatting-results', `المبلغ الأصلي: ${amount}`, 'info');
                addResult('formatting-results', `المبلغ المنسق: ${formatted}`, 'info');
                
                // اختبار قيم مختلفة
                const testValues = [0.01, 0.99, 1.50, 10.25, 100.75, 1000.99];
                addResult('formatting-results', 'اختبار قيم مختلفة:', 'info');
                
                testValues.forEach(value => {
                    const formatted = db.formatCurrencySync(value);
                    addResult('formatting-results', `${value} → ${formatted}`, 'info');
                });
                
            } catch (error) {
                addResult('formatting-results', `✗ خطأ في تنسيق العملة: ${error.message}`, 'error');
            }
        }
        
        async function testDataPersistence() {
            clearResults('persistence-results');
            addResult('persistence-results', 'اختبار حفظ واسترجاع البيانات...', 'info');
            
            try {
                // إنشاء منتج بسعر عشري
                const testProduct = {
                    name: 'منتج اختبار الحفظ',
                    description: 'اختبار حفظ الأرقام العشرية',
                    category: 'اختبار',
                    price: 25.99,
                    quantity: 5,
                    barcode: 'TEST123'
                };
                
                // حفظ المنتج
                await db.saveProduct(testProduct);
                addResult('persistence-results', '✓ تم حفظ المنتج', 'success');
                
                // استرجاع المنتج
                const products = await db.getProducts();
                const savedProduct = products.find(p => p.name === testProduct.name);
                
                if (savedProduct) {
                    addResult('persistence-results', '✓ تم استرجاع المنتج', 'success');
                    addResult('persistence-results', `السعر المحفوظ: ${savedProduct.price}`, 'info');
                    addResult('persistence-results', `نوع البيانات: ${typeof savedProduct.price}`, 'info');
                    
                    // التحقق من دقة السعر
                    if (savedProduct.price === testProduct.price) {
                        addResult('persistence-results', '✓ السعر العشري محفوظ بدقة', 'success');
                    } else {
                        addResult('persistence-results', '✗ السعر العشري لم يحفظ بدقة', 'error');
                    }
                    
                    // التحقق من أن السعر رقم وليس نص
                    if (typeof savedProduct.price === 'number') {
                        addResult('persistence-results', '✓ السعر محفوظ كرقم', 'success');
                    } else {
                        addResult('persistence-results', '✗ السعر محفوظ كنص', 'error');
                    }
                } else {
                    addResult('persistence-results', '✗ لم يتم العثور على المنتج المحفوظ', 'error');
                }
                
            } catch (error) {
                addResult('persistence-results', `✗ خطأ في اختبار الحفظ: ${error.message}`, 'error');
            }
        }
        
        // تشغيل اختبار أولي عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(() => {
                if (typeof db !== 'undefined') {
                    addResult('product-test-results', 'النظام جاهز للاختبار', 'success');
                } else {
                    addResult('product-test-results', 'انتظار تهيئة قاعدة البيانات...', 'info');
                }
            }, 1000);
        });
    </script>
</body>
</html>
