// وظائف مساعدة للتطبيق
class AppHelpers {
    // إنشاء نموذج (فورم) ديناميكي
    static createForm(fields, onSubmit, submitText = 'حفظ') {
        const form = document.createElement('form');
        form.className = 'dynamic-form';
        
        fields.forEach(field => {
            const formGroup = document.createElement('div');
            formGroup.className = 'form-group';
            
            const label = document.createElement('label');
            label.textContent = field.label;
            label.setAttribute('for', field.name);
            formGroup.appendChild(label);
            
            let input;
            
            switch (field.type) {
                case 'select':
                    input = document.createElement('select');
                    field.options.forEach(option => {
                        const optionElement = document.createElement('option');
                        optionElement.value = option.value;
                        optionElement.textContent = option.text;
                        input.appendChild(optionElement);
                    });
                    break;
                case 'textarea':
                    input = document.createElement('textarea');
                    input.rows = field.rows || 3;
                    break;
                default:
                    input = document.createElement('input');
                    input.type = field.type || 'text';
            }
            
            input.name = field.name;
            input.id = field.name;
            input.required = field.required || false;
            input.placeholder = field.placeholder || '';
            
            if (field.value !== undefined) {
                input.value = field.value;
            }
            
            formGroup.appendChild(input);
            form.appendChild(formGroup);
        });
        
        const submitButton = document.createElement('button');
        submitButton.type = 'submit';
        submitButton.className = 'btn btn-primary';
        submitButton.innerHTML = `<i class="fas fa-save"></i> ${submitText}`;
        
        form.appendChild(submitButton);
        
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            const formData = new FormData(form);
            const data = {};
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            onSubmit(data);
        });
        
        return form;
    }
    
    // إنشاء بطاقة إحصائية
    static createStatCard(icon, value, label, color = 'primary') {
        const card = document.createElement('div');
        card.className = 'stat-card';
        
        card.innerHTML = `
            <div class="stat-icon" style="color: var(--${color}-color)">
                <i class="${icon}"></i>
            </div>
            <div class="stat-value">${value}</div>
            <div class="stat-label">${label}</div>
        `;
        
        return card;
    }
    
    // إنشاء بطاقة عادية
    static createCard(title, content, actions = []) {
        const card = document.createElement('div');
        card.className = 'card';
        
        const header = document.createElement('div');
        header.className = 'card-header';
        
        const titleElement = document.createElement('h3');
        titleElement.className = 'card-title';
        titleElement.textContent = title;
        header.appendChild(titleElement);
        
        if (actions.length > 0) {
            const actionsContainer = document.createElement('div');
            actionsContainer.className = 'card-actions';
            
            actions.forEach(action => {
                const button = document.createElement('button');
                button.className = `btn btn-${action.type || 'primary'}`;
                button.innerHTML = `<i class="${action.icon}"></i> ${action.text}`;
                button.addEventListener('click', action.handler);
                actionsContainer.appendChild(button);
            });
            
            header.appendChild(actionsContainer);
        }
        
        card.appendChild(header);
        
        const body = document.createElement('div');
        body.className = 'card-body';
        
        if (typeof content === 'string') {
            body.innerHTML = content;
        } else {
            body.appendChild(content);
        }
        
        card.appendChild(body);
        
        return card;
    }
    
    // إنشاء جدول بيانات مع البحث والفلترة
    static createDataTable(headers, data, options = {}) {
        const container = document.createElement('div');
        container.className = 'data-table-container';
        
        // شريط البحث والفلترة
        if (options.searchable) {
            const searchContainer = document.createElement('div');
            searchContainer.className = 'search-container';
            searchContainer.style.marginBottom = '1rem';
            
            const searchInput = document.createElement('input');
            searchInput.type = 'text';
            searchInput.placeholder = 'البحث...';
            searchInput.className = 'search-input';
            searchInput.style.width = '300px';
            searchInput.style.padding = '0.5rem';
            searchInput.style.border = '2px solid #e0e0e0';
            searchInput.style.borderRadius = '8px';
            
            searchContainer.appendChild(searchInput);
            container.appendChild(searchContainer);
            
            // وظيفة البحث
            searchInput.addEventListener('input', (e) => {
                const searchTerm = e.target.value.toLowerCase();
                const rows = table.querySelectorAll('tbody tr');
                
                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    row.style.display = text.includes(searchTerm) ? '' : 'none';
                });
            });
        }
        
        // إنشاء الجدول
        const table = document.createElement('table');
        table.className = 'table';
        
        // رأس الجدول
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');
        
        headers.forEach(header => {
            const th = document.createElement('th');
            th.textContent = header;
            headerRow.appendChild(th);
        });
        
        if (options.actions) {
            const actionTh = document.createElement('th');
            actionTh.textContent = 'الإجراءات';
            headerRow.appendChild(actionTh);
        }
        
        thead.appendChild(headerRow);
        table.appendChild(thead);
        
        // جسم الجدول
        const tbody = document.createElement('tbody');
        
        data.forEach((row, index) => {
            const tr = document.createElement('tr');
            
            row.forEach(cell => {
                const td = document.createElement('td');
                td.innerHTML = cell;
                tr.appendChild(td);
            });
            
            if (options.actions) {
                const actionTd = document.createElement('td');
                
                options.actions.forEach(action => {
                    const button = document.createElement('button');
                    button.className = `btn btn-${action.type || 'primary'}`;
                    button.innerHTML = `<i class="${action.icon}"></i> ${action.text}`;
                    button.style.marginLeft = '0.5rem';
                    button.addEventListener('click', () => action.handler(data[index], index));
                    actionTd.appendChild(button);
                });
                
                tr.appendChild(actionTd);
            }
            
            tbody.appendChild(tr);
        });
        
        table.appendChild(tbody);
        container.appendChild(table);
        
        return container;
    }
    
    // إنشاء نافذة منبثقة مخصصة
    static createModal(title, content, actions = []) {
        const modal = document.createElement('div');
        modal.className = 'modal';
        
        const modalContent = document.createElement('div');
        modalContent.className = 'modal-content';
        
        const header = document.createElement('div');
        header.className = 'modal-header';
        
        const titleElement = document.createElement('h3');
        titleElement.textContent = title;
        header.appendChild(titleElement);
        
        const closeButton = document.createElement('button');
        closeButton.className = 'modal-close';
        closeButton.innerHTML = '&times;';
        closeButton.addEventListener('click', () => {
            modal.classList.remove('show');
            setTimeout(() => modal.remove(), 300);
        });
        header.appendChild(closeButton);
        
        modalContent.appendChild(header);
        
        const body = document.createElement('div');
        body.className = 'modal-body';
        
        if (typeof content === 'string') {
            body.innerHTML = content;
        } else {
            body.appendChild(content);
        }
        
        modalContent.appendChild(body);
        
        if (actions.length > 0) {
            const footer = document.createElement('div');
            footer.className = 'modal-footer';
            
            actions.forEach(action => {
                const button = document.createElement('button');
                button.className = `btn btn-${action.type || 'primary'}`;
                button.innerHTML = `<i class="${action.icon || ''}"></i> ${action.text}`;
                button.addEventListener('click', () => {
                    if (action.handler) action.handler();
                    if (action.close !== false) {
                        modal.classList.remove('show');
                        setTimeout(() => modal.remove(), 300);
                    }
                });
                footer.appendChild(button);
            });
            
            modalContent.appendChild(footer);
        }
        
        modal.appendChild(modalContent);
        document.body.appendChild(modal);
        
        // إظهار النافذة
        setTimeout(() => modal.classList.add('show'), 10);
        
        return modal;
    }
    
    // تحديد العناصر المحددة في الجدول
    static makeTableSelectable(table, onSelectionChange) {
        const rows = table.querySelectorAll('tbody tr');
        const selectedRows = new Set();
        
        // إضافة عمود التحديد
        const headerRow = table.querySelector('thead tr');
        const selectAllTh = document.createElement('th');
        selectAllTh.innerHTML = '<input type="checkbox" id="selectAll">';
        headerRow.insertBefore(selectAllTh, headerRow.firstChild);
        
        const selectAllCheckbox = selectAllTh.querySelector('input');
        
        rows.forEach((row, index) => {
            const selectTd = document.createElement('td');
            selectTd.innerHTML = `<input type="checkbox" data-index="${index}">`;
            row.insertBefore(selectTd, row.firstChild);
            
            const checkbox = selectTd.querySelector('input');
            checkbox.addEventListener('change', () => {
                if (checkbox.checked) {
                    selectedRows.add(index);
                    row.classList.add('selected');
                } else {
                    selectedRows.delete(index);
                    row.classList.remove('selected');
                }
                
                selectAllCheckbox.checked = selectedRows.size === rows.length;
                selectAllCheckbox.indeterminate = selectedRows.size > 0 && selectedRows.size < rows.length;
                
                if (onSelectionChange) {
                    onSelectionChange(Array.from(selectedRows));
                }
            });
        });
        
        selectAllCheckbox.addEventListener('change', () => {
            const checkboxes = table.querySelectorAll('tbody input[type="checkbox"]');
            checkboxes.forEach((checkbox, index) => {
                checkbox.checked = selectAllCheckbox.checked;
                if (selectAllCheckbox.checked) {
                    selectedRows.add(index);
                    checkbox.closest('tr').classList.add('selected');
                } else {
                    selectedRows.delete(index);
                    checkbox.closest('tr').classList.remove('selected');
                }
            });
            
            if (onSelectionChange) {
                onSelectionChange(Array.from(selectedRows));
            }
        });
        
        return selectedRows;
    }
    
    // تصدير الجدول إلى CSV
    static exportTableToCSV(table, filename = 'data.csv') {
        const rows = [];
        const tableRows = table.querySelectorAll('tr');
        
        tableRows.forEach(row => {
            const cols = row.querySelectorAll('td, th');
            const rowData = [];
            cols.forEach(col => {
                rowData.push(col.textContent.trim());
            });
            rows.push(rowData.join(','));
        });
        
        const csvContent = rows.join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        
        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }
    
    // إنشاء مخطط بياني بسيط
    static createSimpleChart(data, type = 'bar') {
        const canvas = document.createElement('canvas');
        canvas.width = 400;
        canvas.height = 200;
        const ctx = canvas.getContext('2d');
        
        // رسم مخطط بياني بسيط
        const maxValue = Math.max(...data.map(item => item.value));
        const barWidth = canvas.width / data.length;
        
        data.forEach((item, index) => {
            const barHeight = (item.value / maxValue) * (canvas.height - 40);
            const x = index * barWidth;
            const y = canvas.height - barHeight - 20;
            
            // رسم العمود
            ctx.fillStyle = `hsl(${index * 60}, 70%, 50%)`;
            ctx.fillRect(x + 10, y, barWidth - 20, barHeight);
            
            // رسم التسمية
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(item.label, x + barWidth / 2, canvas.height - 5);
            
            // رسم القيمة
            ctx.fillText(item.value, x + barWidth / 2, y - 5);
        });
        
        return canvas;
    }
    
    // التحقق من صحة البيانات
    static validateForm(form, rules) {
        const errors = [];
        const formData = new FormData(form);
        
        for (let [field, rule] of Object.entries(rules)) {
            const value = formData.get(field);
            
            if (rule.required && (!value || value.trim() === '')) {
                errors.push(`${rule.label} مطلوب`);
                continue;
            }
            
            if (value && rule.minLength && value.length < rule.minLength) {
                errors.push(`${rule.label} يجب أن يكون ${rule.minLength} أحرف على الأقل`);
            }
            
            if (value && rule.maxLength && value.length > rule.maxLength) {
                errors.push(`${rule.label} يجب أن يكون ${rule.maxLength} أحرف كحد أقصى`);
            }
            
            if (value && rule.pattern && !rule.pattern.test(value)) {
                errors.push(`${rule.label} غير صحيح`);
            }
            
            if (value && rule.min && parseFloat(value) < rule.min) {
                errors.push(`${rule.label} يجب أن يكون ${rule.min} كحد أدنى`);
            }
            
            if (value && rule.max && parseFloat(value) > rule.max) {
                errors.push(`${rule.label} يجب أن يكون ${rule.max} كحد أقصى`);
            }
        }
        
        return errors;
    }
    
    // تنسيق الأرقام
    static formatNumber(number, decimals = 2) {
        return parseFloat(number).toFixed(decimals);
    }
    
    // حساب النسبة المئوية
    static calculatePercentage(value, total) {
        if (total === 0) return 0;
        return ((value / total) * 100).toFixed(1);
    }
    
    // تحويل التاريخ إلى نص نسبي
    static timeAgo(date) {
        const now = new Date();
        const diffInSeconds = Math.floor((now - new Date(date)) / 1000);
        
        if (diffInSeconds < 60) return 'منذ لحظات';
        if (diffInSeconds < 3600) return `منذ ${Math.floor(diffInSeconds / 60)} دقيقة`;
        if (diffInSeconds < 86400) return `منذ ${Math.floor(diffInSeconds / 3600)} ساعة`;
        if (diffInSeconds < 2592000) return `منذ ${Math.floor(diffInSeconds / 86400)} يوم`;
        if (diffInSeconds < 31536000) return `منذ ${Math.floor(diffInSeconds / 2592000)} شهر`;
        return `منذ ${Math.floor(diffInSeconds / 31536000)} سنة`;
    }
}
