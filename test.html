<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام BakryAfand POS</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار نظام BakryAfand POS</h1>
        <p>هذه الصفحة لاختبار وظائف النظام الأساسية</p>
        
        <div id="test-results"></div>
        
        <button onclick="runTests()">تشغيل الاختبارات</button>
        <button onclick="clearResults()">مسح النتائج</button>
        <button onclick="window.location.href='index.html'">العودة للنظام</button>
    </div>

    <script src="database.js"></script>
    <script>
        let testResults = document.getElementById('test-results');
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            testResults.appendChild(div);
        }
        
        function clearResults() {
            testResults.innerHTML = '';
        }
        
        async function runTests() {
            clearResults();
            addResult('بدء تشغيل الاختبارات...', 'info');
            
            try {
                // اختبار تهيئة قاعدة البيانات
                addResult('اختبار تهيئة قاعدة البيانات...', 'info');
                if (typeof db !== 'undefined') {
                    addResult('✓ تم تهيئة قاعدة البيانات بنجاح', 'success');
                } else {
                    addResult('✗ فشل في تهيئة قاعدة البيانات', 'error');
                    return;
                }
                
                // اختبار الحصول على الإعدادات
                addResult('اختبار الحصول على الإعدادات...', 'info');
                const settings = await db.getSettings();
                if (settings && settings.companyName) {
                    addResult(`✓ تم الحصول على الإعدادات: ${settings.companyName}`, 'success');
                } else {
                    addResult('✗ فشل في الحصول على الإعدادات', 'error');
                }
                
                // اختبار الحصول على المنتجات
                addResult('اختبار الحصول على المنتجات...', 'info');
                const products = await db.getProducts();
                if (products && Array.isArray(products)) {
                    addResult(`✓ تم الحصول على ${products.length} منتج`, 'success');
                } else {
                    addResult('✗ فشل في الحصول على المنتجات', 'error');
                }
                
                // اختبار الحصول على العملاء
                addResult('اختبار الحصول على العملاء...', 'info');
                const customers = await db.getCustomers();
                if (customers && Array.isArray(customers)) {
                    addResult(`✓ تم الحصول على ${customers.length} عميل`, 'success');
                } else {
                    addResult('✗ فشل في الحصول على العملاء', 'error');
                }
                
                // اختبار الإحصائيات
                addResult('اختبار الإحصائيات السريعة...', 'info');
                const stats = await db.getQuickStats();
                if (stats && typeof stats.totalProducts !== 'undefined') {
                    addResult(`✓ الإحصائيات: ${stats.totalProducts} منتج، ${stats.totalCustomers} عميل`, 'success');
                } else {
                    addResult('✗ فشل في الحصول على الإحصائيات', 'error');
                }
                
                // اختبار تنسيق العملة
                addResult('اختبار تنسيق العملة...', 'info');
                const formattedCurrency = db.formatCurrencySync(123.45);
                if (formattedCurrency.includes('١٢٣.٤٥')) {
                    addResult(`✓ تنسيق العملة يعمل: ${formattedCurrency}`, 'success');
                } else {
                    addResult('✗ مشكلة في تنسيق العملة', 'error');
                }

                // اختبار تحميل المنتجات
                addResult('اختبار تحميل المنتجات...', 'info');
                if (typeof loadProducts === 'function') {
                    addResult('✓ دالة loadProducts موجودة', 'success');
                } else {
                    addResult('✗ دالة loadProducts غير موجودة', 'error');
                }

                // اختبار تحميل المبيعات
                addResult('اختبار تحميل المبيعات...', 'info');
                if (typeof loadSales === 'function') {
                    addResult('✓ دالة loadSales موجودة', 'success');
                } else {
                    addResult('✗ دالة loadSales غير موجودة', 'error');
                }

                // اختبار تحميل المشتريات
                addResult('اختبار تحميل المشتريات...', 'info');
                if (typeof loadPurchases === 'function') {
                    addResult('✓ دالة loadPurchases موجودة', 'success');
                } else {
                    addResult('✗ دالة loadPurchases غير موجودة', 'error');
                }
                
                // اختبار تنسيق التاريخ
                addResult('اختبار تنسيق التاريخ...', 'info');
                const formattedDate = db.formatDate(new Date());
                if (formattedDate.includes('/')) {
                    addResult(`✓ تنسيق التاريخ يعمل: ${formattedDate}`, 'success');
                } else {
                    addResult('✗ مشكلة في تنسيق التاريخ', 'error');
                }
                
                addResult('تم الانتهاء من جميع الاختبارات!', 'success');
                
            } catch (error) {
                addResult(`خطأ في الاختبارات: ${error.message}`, 'error');
                console.error('خطأ في الاختبارات:', error);
            }
        }
        
        // تشغيل الاختبارات تلقائياً عند تحميل الصفحة
        window.addEventListener('load', () => {
            // انتظار تهيئة قاعدة البيانات
            setTimeout(() => {
                if (typeof db !== 'undefined') {
                    runTests();
                } else {
                    addResult('انتظار تهيئة قاعدة البيانات...', 'info');
                    window.addEventListener('databaseReady', runTests);
                }
            }, 1000);
        });
    </script>
</body>
</html>
