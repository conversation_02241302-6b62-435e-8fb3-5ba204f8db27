# دليل ترقية نظام BakryAfand POS

## التحديثات الجديدة

تم تحديث نظام BakryAfand POS لدعم تخزين البيانات بشكل دائم في ملفات JSON بدلاً من localStorage، مما يوفر:

### المزايا الجديدة:
- **تخزين دائم**: البيانات محفوظة في ملفات JSON على القرص الصلب
- **نسخ احتياطية تلقائية**: إمكانية إنشاء واستعادة النسخ الاحتياطية
- **أداء محسن**: نظام كاش ذكي لتحسين سرعة الوصول للبيانات
- **أمان أفضل**: البيانات محمية ومخزنة خارج المتصفح

### مسار تخزين البيانات:
- **Windows**: `C:\Users\<USER>\BakryAfand-POS-Data\`
- **macOS**: `/Users/<USER>/BakryAfand-POS-Data/`
- **Linux**: `/home/<USER>/BakryAfand-POS-Data/`

## ملفات البيانات:

### الملفات الأساسية:
- `products.json` - بيانات المنتجات
- `customers.json` - بيانات العملاء
- `suppliers.json` - بيانات الموردين
- `sales.json` - بيانات المبيعات
- `purchases.json` - بيانات المشتريات
- `payments.json` - بيانات المدفوعات
- `settings.json` - إعدادات النظام

### مجلد النسخ الاحتياطية:
- `backups/` - يحتوي على النسخ الاحتياطية التلقائية

## كيفية التشغيل:

### 1. تشغيل التطبيق كـ Electron App:
```bash
npm install
npm start
```

### 2. تشغيل في المتصفح (وضع التطوير):
```bash
# باستخدام Python
python -m http.server 8000

# باستخدام Node.js
npx http-server
```

## الميزات الجديدة:

### 1. النسخ الاحتياطية التلقائية:
- يتم إنشاء نسخة احتياطية تلقائياً عند كل تغيير مهم
- يمكن استعادة أي نسخة احتياطية من الإعدادات
- النسخ محفوظة بتاريخ ووقت الإنشاء

### 2. تحسينات الأداء:
- نظام كاش ذكي يقلل من عمليات القراءة من القرص
- تحميل البيانات بشكل غير متزامن (async)
- تحسين استجابة الواجهة

### 3. معالجة الأخطاء:
- معالجة شاملة للأخطاء مع رسائل واضحة
- آلية استرداد في حالة فشل العمليات
- تسجيل مفصل للأخطاء في وحدة التحكم

## التوافق مع الإصدارات السابقة:

### الترقية التلقائية:
- عند التشغيل لأول مرة، سيتم نقل البيانات من localStorage تلقائياً
- لا حاجة لإعادة إدخال البيانات
- جميع الإعدادات والبيانات محفوظة

### في حالة وجود مشاكل:
1. تأكد من وجود صلاحيات الكتابة في مجلد المستخدم
2. تحقق من وجود مساحة كافية على القرص
3. راجع رسائل الأخطاء في وحدة التحكم

## استكشاف الأخطاء:

### مشكلة: لا يتم حفظ البيانات
**الحل:**
- تأكد من تشغيل التطبيق كـ Electron App
- تحقق من صلاحيات الكتابة في مجلد البيانات
- أعد تشغيل التطبيق

### مشكلة: بطء في التحميل
**الحل:**
- امسح الكاش من الإعدادات
- تأكد من عدم وجود ملفات كبيرة في مجلد البيانات
- أعد تشغيل التطبيق

### مشكلة: فقدان البيانات
**الحل:**
- تحقق من مجلد النسخ الاحتياطية
- استعد آخر نسخة احتياطية من الإعدادات
- تواصل مع الدعم الفني

## الدعم الفني:

للحصول على المساعدة:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +968 9999 9999

## ملاحظات مهمة:

1. **النسخ الاحتياطية**: يُنصح بإنشاء نسخة احتياطية يدوية قبل أي تحديث كبير
2. **الأمان**: لا تشارك ملفات البيانات مع أشخاص غير مخولين
3. **الصيانة**: امسح النسخ الاحتياطية القديمة بانتظام لتوفير المساحة
4. **التحديثات**: تحقق من التحديثات الجديدة بانتظام

---

**تاريخ آخر تحديث:** 2025-07-12
**إصدار النظام:** 2.0.0
