{"name": "bakryafand-pos", "version": "1.0.0", "description": "نظام إدارة نقاط البيع العربي - BakryAfand POS System", "main": "main-electron.js", "homepage": "./", "author": {"name": "<PERSON>k<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"start": "electron .", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "dist": "npm run build", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps"}, "build": {"appId": "com.bakryafand.pos", "productName": "BakryAfand POS", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules", "!dist", "!.git", "!README.md", "!package-lock.json"], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}], "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "BakryAfand POS", "deleteAppDataOnUninstall": false, "runAfterFinish": true, "menuCategory": "Business", "artifactName": "BakryAfand-POS-Setup-${version}.${ext}", "displayLanguageSelector": false, "language": "1025"}, "portable": {"artifactName": "${productName}-${version}-portable.${ext}"}, "mac": {"target": "dmg", "category": "public.app-category.business"}, "linux": {"target": "AppImage", "category": "Office"}, "extraResources": [{"from": "assets/", "to": "assets/", "filter": ["**/*"]}]}, "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"electron-updater": "^6.1.4"}}