// إدارة العملاء
async function loadCustomers() {
    const pageContent = document.getElementById('pageContent');

    // إنشاء شريط الأدوات
    const toolbar = createCustomersToolbar();
    pageContent.appendChild(toolbar);

    // إنشاء جدول العملاء
    const customersTable = await createCustomersTable();
    pageContent.appendChild(customersTable);
}

// إنشاء شريط أدوات العملاء
function createCustomersToolbar() {
    const toolbar = document.createElement('div');
    toolbar.className = 'card';
    toolbar.style.marginBottom = '1.5rem';

    const toolbarContent = document.createElement('div');
    toolbarContent.style.display = 'flex';
    toolbarContent.style.justifyContent = 'space-between';
    toolbarContent.style.alignItems = 'center';
    toolbarContent.style.flexWrap = 'wrap';
    toolbarContent.style.gap = '1rem';

    // الجانب الأيسر - البحث والفلترة
    const leftSide = document.createElement('div');
    leftSide.style.display = 'flex';
    leftSide.style.alignItems = 'center';
    leftSide.style.gap = '1rem';
    leftSide.style.flex = '1';

    // حقل البحث
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.placeholder = 'البحث في العملاء...';
    searchInput.className = 'search-input';
    searchInput.style.maxWidth = '300px';
    searchInput.id = 'customerSearch';

    // فلتر الرصيد
    const balanceFilter = document.createElement('select');
    balanceFilter.className = 'filter-select';
    balanceFilter.id = 'balanceFilter';
    balanceFilter.innerHTML = `
        <option value="">جميع العملاء</option>
        <option value="positive">عملاء مدينون</option>
        <option value="negative">عملاء دائنون</option>
        <option value="zero">رصيد صفر</option>
    `;

    leftSide.appendChild(searchInput);
    leftSide.appendChild(balanceFilter);

    // الجانب الأيمن - الأزرار
    const rightSide = document.createElement('div');
    rightSide.style.display = 'flex';
    rightSide.style.alignItems = 'center';
    rightSide.style.gap = '0.5rem';

    // زر إضافة عميل جديد
    const addButton = document.createElement('button');
    addButton.className = 'btn btn-primary';
    addButton.innerHTML = '<i class="fas fa-plus"></i> إضافة عميل';
    addButton.addEventListener('click', () => showCustomerModal());

    // زر تصدير
    const exportButton = document.createElement('button');
    exportButton.className = 'btn btn-success';
    exportButton.innerHTML = '<i class="fas fa-download"></i> تصدير';
    exportButton.addEventListener('click', exportCustomers);

    // زر طباعة
    const printButton = document.createElement('button');
    printButton.className = 'btn btn-info';
    printButton.innerHTML = '<i class="fas fa-print"></i> طباعة';
    printButton.addEventListener('click', printCustomers);

    rightSide.appendChild(addButton);
    rightSide.appendChild(exportButton);
    rightSide.appendChild(printButton);

    toolbarContent.appendChild(leftSide);
    toolbarContent.appendChild(rightSide);
    toolbar.appendChild(toolbarContent);

    // إضافة مستمعي الأحداث للبحث والفلترة
    searchInput.addEventListener('input', filterCustomers);
    balanceFilter.addEventListener('change', filterCustomers);

    return toolbar;
}

// إنشاء جدول العملاء
async function createCustomersTable() {
    const customers = (await db.getCustomers()).filter(c => c.id !== 'guest'); // استثناء العميل الافتراضي

    const tableContainer = document.createElement('div');
    tableContainer.className = 'card';

    if (customers.length === 0) {
        tableContainer.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-users"></i>
                <h3>لا توجد عملاء</h3>
                <p>ابدأ بإضافة عملاء جدد لمتجرك</p>
                <button class="btn btn-primary" onclick="showCustomerModal()">
                    <i class="fas fa-plus"></i> إضافة أول عميل
                </button>
            </div>
        `;
        return tableContainer;
    }

    const table = document.createElement('table');
    table.className = 'table';
    table.id = 'customersTable';

    // رأس الجدول
    table.innerHTML = `
        <thead>
            <tr>
                <th>اسم العميل</th>
                <th>الهاتف</th>
                <th>البريد الإلكتروني</th>
                <th>العنوان</th>
                <th>الرصيد</th>
                <th>آخر معاملة</th>
                <th>تاريخ التسجيل</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            ${customers.map(customer => createCustomerRow(customer)).join('')}
        </tbody>
    `;

    tableContainer.appendChild(table);
    return tableContainer;
}

// إنشاء صف عميل
function createCustomerRow(customer) {
    const sales = db.getSales().filter(s => s.customerId === customer.id);
    const lastSale = sales.length > 0 ? sales[sales.length - 1] : null;

    const balanceClass = customer.balance > 0 ? 'badge-danger' :
                        customer.balance < 0 ? 'badge-success' : 'badge-secondary';

    const balanceText = customer.balance > 0 ? 'مدين' :
                       customer.balance < 0 ? 'دائن' : 'متوازن';

    return `
        <tr data-customer-id="${customer.id}">
            <td>
                <div>
                    <strong>${customer.name}</strong>
                    <br><small style="color: #666;">ID: ${customer.id.slice(-6)}</small>
                </div>
            </td>
            <td>${customer.phone || '-'}</td>
            <td>${customer.email || '-'}</td>
            <td>${customer.address || '-'}</td>
            <td>
                <span class="badge ${balanceClass}">
                    ${db.formatCurrency(Math.abs(customer.balance))} ${balanceText}
                </span>
            </td>
            <td>${lastSale ? db.formatDate(lastSale.createdAt) : 'لا توجد معاملات'}</td>
            <td>${db.formatDate(customer.createdAt)}</td>
            <td>
                <button class="btn btn-info" onclick="viewCustomer('${customer.id}')" title="عرض">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-warning" onclick="editCustomer('${customer.id}')" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-success" onclick="addPayment('${customer.id}')" title="دفعة">
                    <i class="fas fa-money-bill"></i>
                </button>
                <button class="btn btn-danger" onclick="deleteCustomer('${customer.id}')" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `;
}

// فلترة العملاء
function filterCustomers() {
    const searchTerm = document.getElementById('customerSearch').value.toLowerCase();
    const balanceFilter = document.getElementById('balanceFilter').value;
    const rows = document.querySelectorAll('#customersTable tbody tr');

    rows.forEach(row => {
        const customerId = row.dataset.customerId;
        const customer = db.getCustomerById(customerId);

        const customerName = customer.name.toLowerCase();
        const customerPhone = (customer.phone || '').toLowerCase();
        const customerEmail = (customer.email || '').toLowerCase();

        const matchesSearch = customerName.includes(searchTerm) ||
                             customerPhone.includes(searchTerm) ||
                             customerEmail.includes(searchTerm);

        let matchesBalance = true;
        if (balanceFilter) {
            switch (balanceFilter) {
                case 'positive':
                    matchesBalance = customer.balance > 0;
                    break;
                case 'negative':
                    matchesBalance = customer.balance < 0;
                    break;
                case 'zero':
                    matchesBalance = customer.balance === 0;
                    break;
            }
        }

        row.style.display = matchesSearch && matchesBalance ? '' : 'none';
    });
}

// عرض نافذة إضافة/تعديل عميل
function showCustomerModal(customerId = null) {
    const isEdit = !!customerId;
    const customer = isEdit ? db.getCustomerById(customerId) : {};

    const fields = [
        {
            name: 'name',
            label: 'اسم العميل',
            type: 'text',
            required: true,
            value: customer.name || '',
            placeholder: 'أدخل اسم العميل'
        },
        {
            name: 'phone',
            label: 'رقم الهاتف',
            type: 'tel',
            value: customer.phone || '',
            placeholder: 'رقم الهاتف'
        },
        {
            name: 'email',
            label: 'البريد الإلكتروني',
            type: 'email',
            value: customer.email || '',
            placeholder: 'البريد الإلكتروني'
        },
        {
            name: 'address',
            label: 'العنوان',
            type: 'textarea',
            value: customer.address || '',
            placeholder: 'عنوان العميل',
            rows: 3
        }
    ];

    const form = AppHelpers.createForm(fields, (data) => {
        saveCustomer(data, customerId);
    }, isEdit ? 'تحديث' : 'إضافة');

    const actions = [
        {
            text: 'إلغاء',
            type: 'secondary',
            handler: () => {}
        }
    ];

    AppHelpers.createModal(
        isEdit ? 'تعديل العميل' : 'إضافة عميل جديد',
        form,
        actions
    );
}

// حفظ العميل
function saveCustomer(data, customerId = null) {
    try {
        const customerData = {
            name: data.name.trim(),
            phone: data.phone.trim(),
            email: data.email.trim(),
            address: data.address.trim()
        };

        if (customerId) {
            customerData.id = customerId;
        }

        db.saveCustomer(customerData);

        app.showAlert(
            customerId ? 'تم تحديث العميل بنجاح' : 'تم إضافة العميل بنجاح',
            'success'
        );

        loadCustomers(); // إعادة تحميل الصفحة
    } catch (error) {
        app.showAlert('حدث خطأ أثناء حفظ العميل', 'danger');
    }
}

// تعديل عميل
function editCustomer(customerId) {
    showCustomerModal(customerId);
}

// حذف عميل
function deleteCustomer(customerId) {
    const customer = db.getCustomerById(customerId);
    if (!customer) return;

    // التحقق من وجود معاملات للعميل
    const sales = db.getSales().filter(s => s.customerId === customerId);
    if (sales.length > 0) {
        app.showAlert('لا يمكن حذف العميل لوجود معاملات مرتبطة به', 'warning');
        return;
    }

    app.showConfirm(
        `هل أنت متأكد من حذف العميل "${customer.name}"؟`,
        () => {
            db.deleteCustomer(customerId);
            app.showAlert('تم حذف العميل بنجاح', 'success');
            loadCustomers();
        },
        'تأكيد الحذف'
    );
}

// عرض تفاصيل العميل
function viewCustomer(customerId) {
    const customer = db.getCustomerById(customerId);
    if (!customer) return;

    const sales = db.getSales().filter(s => s.customerId === customerId);
    const payments = db.getPayments().filter(p => p.customerId === customerId);

    const totalPurchases = sales.reduce((sum, sale) => sum + sale.total, 0);
    const totalPayments = payments.reduce((sum, payment) => sum + payment.amount, 0);

    const content = `
        <div class="customer-details">
            <div class="customer-info">
                <h4>معلومات العميل</h4>
                <div class="info-grid">
                    <div><strong>الاسم:</strong> ${customer.name}</div>
                    <div><strong>الهاتف:</strong> ${customer.phone || 'غير محدد'}</div>
                    <div><strong>البريد:</strong> ${customer.email || 'غير محدد'}</div>
                    <div><strong>العنوان:</strong> ${customer.address || 'غير محدد'}</div>
                    <div><strong>تاريخ التسجيل:</strong> ${db.formatDate(customer.createdAt)}</div>
                    <div><strong>الرصيد الحالي:</strong>
                        <span class="badge ${customer.balance > 0 ? 'badge-danger' : customer.balance < 0 ? 'badge-success' : 'badge-secondary'}">
                            ${db.formatCurrency(Math.abs(customer.balance))} ${customer.balance > 0 ? 'مدين' : customer.balance < 0 ? 'دائن' : 'متوازن'}
                        </span>
                    </div>
                </div>
            </div>

            <div class="customer-stats">
                <h4>إحصائيات العميل</h4>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-value">${db.toArabicNumbers(sales.length)}</span>
                        <span class="stat-label">عدد المشتريات</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">${db.formatCurrency(totalPurchases)}</span>
                        <span class="stat-label">إجمالي المشتريات</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">${db.formatCurrency(totalPayments)}</span>
                        <span class="stat-label">إجمالي المدفوعات</span>
                    </div>
                </div>
            </div>

            <div class="recent-transactions">
                <h4>آخر المعاملات</h4>
                ${sales.length > 0 ? `
                    <table class="table">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${sales.slice(-5).reverse().map(sale => `
                                <tr>
                                    <td>${db.toArabicNumbers(sale.invoiceNumber || sale.id.slice(-6))}</td>
                                    <td>${db.formatCurrency(sale.total)}</td>
                                    <td>${sale.paymentMethod === 'cash' ? 'نقداً' : 'آجل'}</td>
                                    <td>${db.formatDate(sale.createdAt)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                ` : '<p>لا توجد معاملات</p>'}
            </div>
        </div>

        <style>
            .customer-details h4 {
                margin-bottom: 1rem;
                color: var(--primary-color);
                border-bottom: 2px solid var(--primary-color);
                padding-bottom: 0.5rem;
            }
            .info-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 1rem;
                margin-bottom: 2rem;
            }
            .stats-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 1rem;
                margin-bottom: 2rem;
            }
            .stat-item {
                text-align: center;
                padding: 1rem;
                background: #f8f9fa;
                border-radius: 8px;
            }
            .stat-value {
                display: block;
                font-size: 1.5rem;
                font-weight: bold;
                color: var(--primary-color);
            }
            .stat-label {
                font-size: 0.9rem;
                color: #666;
            }
            .recent-transactions {
                margin-top: 2rem;
            }
        </style>
    `;

    const actions = [
        {
            text: 'تعديل',
            type: 'warning',
            icon: 'fas fa-edit',
            handler: () => editCustomer(customerId),
            close: false
        },
        {
            text: 'إضافة دفعة',
            type: 'success',
            icon: 'fas fa-money-bill',
            handler: () => addPayment(customerId),
            close: false
        },
        {
            text: 'كشف حساب',
            type: 'info',
            icon: 'fas fa-file-alt',
            handler: () => printCustomerStatement(customerId),
            close: false
        },
        {
            text: 'إغلاق',
            type: 'secondary',
            handler: () => {}
        }
    ];

    AppHelpers.createModal(`تفاصيل العميل: ${customer.name}`, content, actions);
}

// إضافة دفعة
function addPayment(customerId) {
    const customer = db.getCustomerById(customerId);
    if (!customer || customer.balance <= 0) {
        app.showAlert('العميل ليس مديناً', 'warning');
        return;
    }

    const fields = [
        {
            name: 'amount',
            label: 'مبلغ الدفعة',
            type: 'number',
            required: true,
            placeholder: '0.00',
            value: customer.balance.toString()
        },
        {
            name: 'notes',
            label: 'ملاحظات',
            type: 'textarea',
            placeholder: 'ملاحظات الدفعة (اختياري)',
            rows: 3
        }
    ];

    const form = AppHelpers.createForm(fields, (data) => {
        const amount = parseFloat(data.amount);
        if (amount <= 0) {
            app.showAlert('يجب أن يكون المبلغ أكبر من صفر', 'warning');
            return;
        }

        if (amount > customer.balance) {
            app.showAlert('المبلغ أكبر من الدين المستحق', 'warning');
            return;
        }

        const payment = {
            customerId: customerId,
            amount: amount,
            notes: data.notes.trim(),
            receiptNumber: generateReceiptNumber()
        };

        db.savePayment(payment);

        app.showAlert('تم تسجيل الدفعة بنجاح', 'success');

        // طباعة إيصال الاستلام
        printPaymentReceipt(payment, customer);

        loadCustomers();
    }, 'تسجيل الدفعة');

    const actions = [
        {
            text: 'إلغاء',
            type: 'secondary',
            handler: () => {}
        }
    ];

    AppHelpers.createModal(`تسجيل دفعة للعميل: ${customer.name}`, form, actions);
}

// إنشاء رقم إيصال
function generateReceiptNumber() {
    const now = new Date();
    const year = now.getFullYear().toString().slice(-2);
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const time = now.getTime().toString().slice(-4);

    return `R${year}${month}${day}${time}`;
}

// طباعة إيصال الاستلام
function printPaymentReceipt(payment, customer) {
    const settings = db.getSettings();

    const content = `
        <div style="text-align: center; margin-bottom: 30px;">
            <h1>${settings.companyName}</h1>
            <p>${settings.companyAddress}</p>
            <p>هاتف: ${settings.companyPhone} | بريد: ${settings.companyEmail}</p>
        </div>

        <h2 style="text-align: center; margin-bottom: 30px;">إيصال استلام مبلغ</h2>

        <div style="margin-bottom: 20px;">
            <p><strong>رقم الإيصال:</strong> ${db.toArabicNumbers(payment.receiptNumber)}</p>
            <p><strong>التاريخ:</strong> ${db.formatDateTime(payment.createdAt)}</p>
            <p><strong>اسم العميل:</strong> ${customer.name}</p>
            <p><strong>المبلغ المستلم:</strong> ${db.formatCurrency(payment.amount)}</p>
            ${payment.notes ? `<p><strong>ملاحظات:</strong> ${payment.notes}</p>` : ''}
        </div>

        <div style="margin-top: 50px; text-align: center;">
            <p>توقيع المستلم: ________________</p>
        </div>

        <div style="text-align: center; margin-top: 30px; font-size: 0.9em;">
            <p>شكراً لتعاملكم معنا</p>
        </div>
    `;

    app.printContent(content, `إيصال رقم ${payment.receiptNumber}`);
}

// طباعة كشف حساب العميل
function printCustomerStatement(customerId) {
    const customer = db.getCustomerById(customerId);
    const sales = db.getSales().filter(s => s.customerId === customerId);
    const payments = db.getPayments().filter(p => p.customerId === customerId);
    const settings = db.getSettings();

    // دمج المعاملات وترتيبها حسب التاريخ
    const transactions = [
        ...sales.map(sale => ({
            type: 'sale',
            date: sale.createdAt,
            description: `فاتورة رقم ${sale.invoiceNumber || sale.id.slice(-6)}`,
            debit: sale.paymentMethod === 'credit' ? sale.total : 0,
            credit: 0
        })),
        ...payments.map(payment => ({
            type: 'payment',
            date: payment.createdAt,
            description: `دفعة - إيصال رقم ${payment.receiptNumber}`,
            debit: 0,
            credit: payment.amount
        }))
    ].sort((a, b) => new Date(a.date) - new Date(b.date));

    let balance = 0;

    const content = `
        <div style="text-align: center; margin-bottom: 30px;">
            <h1>${settings.companyName}</h1>
            <p>${settings.companyAddress}</p>
            <p>هاتف: ${settings.companyPhone} | بريد: ${settings.companyEmail}</p>
        </div>

        <h2 style="text-align: center; margin-bottom: 30px;">كشف حساب العميل</h2>

        <div style="margin-bottom: 20px;">
            <p><strong>اسم العميل:</strong> ${customer.name}</p>
            <p><strong>تاريخ الكشف:</strong> ${db.formatDateTime(new Date())}</p>
            <p><strong>الرصيد الحالي:</strong> ${db.formatCurrency(Math.abs(customer.balance))} ${customer.balance > 0 ? 'مدين' : customer.balance < 0 ? 'دائن' : 'متوازن'}</p>
        </div>

        <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
            <thead>
                <tr style="background-color: #f5f5f5;">
                    <th style="border: 1px solid #ddd; padding: 8px;">التاريخ</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">البيان</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">مدين</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">دائن</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">الرصيد</th>
                </tr>
            </thead>
            <tbody>
                ${transactions.map(transaction => {
                    balance += transaction.debit - transaction.credit;
                    return `
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px;">${db.formatDate(transaction.date)}</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">${transaction.description}</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">${transaction.debit > 0 ? db.formatCurrency(transaction.debit) : '-'}</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">${transaction.credit > 0 ? db.formatCurrency(transaction.credit) : '-'}</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">${db.formatCurrency(Math.abs(balance))} ${balance > 0 ? 'مدين' : balance < 0 ? 'دائن' : ''}</td>
                        </tr>
                    `;
                }).join('')}
            </tbody>
        </table>

        <div style="margin-top: 30px;">
            <p><strong>إجمالي المشتريات:</strong> ${db.formatCurrency(sales.reduce((sum, sale) => sum + sale.total, 0))}</p>
            <p><strong>إجمالي المدفوعات:</strong> ${db.formatCurrency(payments.reduce((sum, payment) => sum + payment.amount, 0))}</p>
            <p><strong>الرصيد النهائي:</strong> ${db.formatCurrency(Math.abs(customer.balance))} ${customer.balance > 0 ? 'مدين' : customer.balance < 0 ? 'دائن' : 'متوازن'}</p>
        </div>
    `;

    app.printContent(content, `كشف حساب ${customer.name}`);
}

// تصدير العملاء
function exportCustomers() {
    const customers = db.getCustomers().filter(c => c.id !== 'guest');

    if (customers.length === 0) {
        app.showAlert('لا توجد عملاء للتصدير', 'warning');
        return;
    }

    const csvData = [
        ['اسم العميل', 'الهاتف', 'البريد الإلكتروني', 'العنوان', 'الرصيد', 'تاريخ التسجيل']
    ];

    customers.forEach(customer => {
        csvData.push([
            customer.name,
            customer.phone || '',
            customer.email || '',
            customer.address || '',
            customer.balance,
            db.formatDate(customer.createdAt)
        ]);
    });

    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `customers_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        app.showAlert('تم تصدير العملاء بنجاح', 'success');
    }
}

// طباعة العملاء
function printCustomers() {
    const customers = db.getCustomers().filter(c => c.id !== 'guest');

    if (customers.length === 0) {
        app.showAlert('لا توجد عملاء للطباعة', 'warning');
        return;
    }

    const content = `
        <h2 style="text-align: center; margin-bottom: 30px;">قائمة العملاء</h2>
        <table style="width: 100%; border-collapse: collapse;">
            <thead>
                <tr style="background-color: #f5f5f5;">
                    <th style="border: 1px solid #ddd; padding: 8px;">اسم العميل</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">الهاتف</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">البريد</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">الرصيد</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">تاريخ التسجيل</th>
                </tr>
            </thead>
            <tbody>
                ${customers.map(customer => `
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px;">${customer.name}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${customer.phone || '-'}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${customer.email || '-'}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${db.formatCurrency(Math.abs(customer.balance))} ${customer.balance > 0 ? 'مدين' : customer.balance < 0 ? 'دائن' : 'متوازن'}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${db.formatDate(customer.createdAt)}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
        <div style="margin-top: 30px; text-align: center;">
            <p><strong>إجمالي العملاء:</strong> ${db.toArabicNumbers(customers.length)} عميل</p>
        </div>
    `;

    app.printContent(content, 'قائمة العملاء');
}