// إدارة الديون والمدفوعات
async function loadDebts() {
    const pageContent = document.getElementById('pageContent');

    // إنشاء تخطيط الديون
    const debtsLayout = document.createElement('div');
    debtsLayout.style.display = 'grid';
    debtsLayout.style.gridTemplateColumns = '1fr 1fr';
    debtsLayout.style.gap = '1.5rem';
    debtsLayout.style.marginBottom = '1.5rem';

    // بطاقة ملخص الديون
    const summaryCard = await createDebtsSummaryCard();
    debtsLayout.appendChild(summaryCard);

    // بطاقة الإجراءات السريعة
    const actionsCard = createQuickActionsCard();
    debtsLayout.appendChild(actionsCard);

    pageContent.appendChild(debtsLayout);

    // جدول العملاء المدينين
    const debtorsTable = await createDebtorsTable();
    pageContent.appendChild(debtorsTable);

    // جدول المدفوعات الأخيرة
    const paymentsTable = await createRecentPaymentsTable();
    pageContent.appendChild(paymentsTable);
}

// إنشاء بطاقة ملخص الديون
async function createDebtsSummaryCard() {
    const customers = await db.getCustomers();
    const debtors = customers.filter(c => c.balance > 0);
    const creditors = customers.filter(c => c.balance < 0);
    
    const totalDebts = debtors.reduce((sum, c) => sum + c.balance, 0);
    const totalCredits = Math.abs(creditors.reduce((sum, c) => sum + c.balance, 0));
    const netBalance = totalDebts - totalCredits;
    
    const content = `
        <div class="debts-summary">
            <div class="summary-item">
                <div class="summary-icon" style="background: #ffebee; color: #d32f2f;">
                    <i class="fas fa-arrow-up"></i>
                </div>
                <div class="summary-details">
                    <h3>${db.formatCurrencySync(totalDebts)}</h3>
                    <p>إجمالي الديون</p>
                    <small>${db.toArabicNumbers(debtors.length)} عميل مدين</small>
                </div>
            </div>
            
            <div class="summary-item">
                <div class="summary-icon" style="background: #e8f5e8; color: #2e7d32;">
                    <i class="fas fa-arrow-down"></i>
                </div>
                <div class="summary-details">
                    <h3>${db.formatCurrencySync(totalCredits)}</h3>
                    <p>إجمالي الدائنين</p>
                    <small>${db.toArabicNumbers(creditors.length)} عميل دائن</small>
                </div>
            </div>
            
            <div class="summary-item">
                <div class="summary-icon" style="background: ${netBalance >= 0 ? '#e3f2fd' : '#fff3e0'}; color: ${netBalance >= 0 ? '#1976d2' : '#f57c00'};">
                    <i class="fas fa-balance-scale"></i>
                </div>
                <div class="summary-details">
                    <h3>${db.formatCurrencySync(Math.abs(netBalance))}</h3>
                    <p>الرصيد الصافي</p>
                    <small>${netBalance >= 0 ? 'لصالح الشركة' : 'على الشركة'}</small>
                </div>
            </div>
        </div>
        
        <style>
            .debts-summary {
                display: flex;
                flex-direction: column;
                gap: 1rem;
            }
            .summary-item {
                display: flex;
                align-items: center;
                gap: 1rem;
                padding: 1rem;
                background: #f8f9fa;
                border-radius: 8px;
                border-right: 4px solid var(--primary-color);
            }
            .summary-icon {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.2rem;
            }
            .summary-details h3 {
                margin: 0;
                font-size: 1.2rem;
                font-weight: bold;
            }
            .summary-details p {
                margin: 0.25rem 0;
                color: #666;
            }
            .summary-details small {
                color: #999;
                font-size: 0.8rem;
            }
        </style>
    `;
    
    return AppHelpers.createCard('ملخص الديون والمدفوعات', content);
}

// إنشاء بطاقة الإجراءات السريعة
function createQuickActionsCard() {
    const content = `
        <div class="quick-actions">
            <button class="action-btn debt-btn" onclick="showAllDebtors()">
                <i class="fas fa-users"></i>
                <span>العملاء المدينين</span>
            </button>
            
            <button class="action-btn payment-btn" onclick="showQuickPayment()">
                <i class="fas fa-money-bill"></i>
                <span>تسجيل دفعة سريعة</span>
            </button>
            
            <button class="action-btn report-btn" onclick="generateDebtsReport()">
                <i class="fas fa-file-alt"></i>
                <span>تقرير الديون</span>
            </button>
            
            <button class="action-btn reminder-btn" onclick="sendPaymentReminders()">
                <i class="fas fa-bell"></i>
                <span>تذكير بالدفع</span>
            </button>
        </div>
        
        <style>
            .quick-actions {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 1rem;
            }
            .action-btn {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 0.5rem;
                padding: 1.5rem 1rem;
                border: none;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.3s ease;
                text-decoration: none;
                color: white;
                font-weight: 500;
            }
            .debt-btn {
                background: linear-gradient(135deg, #f44336, #d32f2f);
            }
            .payment-btn {
                background: linear-gradient(135deg, #4caf50, #388e3c);
            }
            .report-btn {
                background: linear-gradient(135deg, #2196f3, #1976d2);
            }
            .reminder-btn {
                background: linear-gradient(135deg, #ff9800, #f57c00);
            }
            .action-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            }
            .action-btn i {
                font-size: 1.5rem;
            }
        </style>
    `;
    
    return AppHelpers.createCard('إجراءات سريعة', content);
}

// إنشاء جدول العملاء المدينين
async function createDebtorsTable() {
    const customers = (await db.getCustomers()).filter(c => c.balance > 0 && c.id !== 'guest');
    
    const tableContainer = document.createElement('div');
    tableContainer.className = 'card';
    
    const header = document.createElement('div');
    header.className = 'card-header';
    header.innerHTML = `
        <h3 class="card-title">العملاء المدينين</h3>
        <div>
            <button class="btn btn-success" onclick="showBulkPayment()">
                <i class="fas fa-money-bill-wave"></i> دفعات مجمعة
            </button>
        </div>
    `;
    tableContainer.appendChild(header);
    
    if (customers.length === 0) {
        const emptyState = document.createElement('div');
        emptyState.className = 'empty-state';
        emptyState.innerHTML = `
            <i class="fas fa-check-circle" style="color: #4caf50;"></i>
            <h3>لا توجد ديون مستحقة</h3>
            <p>جميع العملاء قاموا بسداد مستحقاتهم</p>
        `;
        tableContainer.appendChild(emptyState);
        return tableContainer;
    }
    
    // ترتيب العملاء حسب المبلغ المستحق (الأكبر أولاً)
    customers.sort((a, b) => b.balance - a.balance);
    
    const table = document.createElement('table');
    table.className = 'table';
    table.innerHTML = `
        <thead>
            <tr>
                <th>اسم العميل</th>
                <th>المبلغ المستحق</th>
                <th>آخر معاملة</th>
                <th>مدة الدين</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody id="debtorsTableBody">
        </tbody>
    `;

    tableContainer.appendChild(table);

    // ملء الجدول بالبيانات
    const tableBody = table.querySelector('#debtorsTableBody');
    const sales = await db.getSales();

    for (const customer of customers) {
        const customerSales = sales.filter(s => s.customerId === customer.id && s.paymentMethod === 'credit');
        const lastSale = customerSales.length > 0 ? customerSales[customerSales.length - 1] : null;
        const daysSinceLastSale = lastSale ? Math.floor((new Date() - new Date(lastSale.createdAt)) / (1000 * 60 * 60 * 24)) : 0;

        let urgencyClass = 'badge-warning';
        if (daysSinceLastSale > 30) urgencyClass = 'badge-danger';
        else if (daysSinceLastSale > 7) urgencyClass = 'badge-warning';
        else urgencyClass = 'badge-info';

        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <strong>${customer.name}</strong>
                <br><small>${customer.phone || 'لا يوجد هاتف'}</small>
            </td>
            <td>
                <span class="badge badge-danger" style="font-size: 1rem;">
                    ${db.formatCurrencySync(customer.balance)}
                </span>
            </td>
            <td>${lastSale ? db.formatDate(lastSale.createdAt) : 'لا توجد معاملات'}</td>
            <td>
                <span class="badge ${urgencyClass}">
                    ${daysSinceLastSale > 0 ? `${db.toArabicNumbers(daysSinceLastSale)} يوم` : 'جديد'}
                </span>
            </td>
            <td>
                <button class="btn btn-success" onclick="addPayment('${customer.id}')" title="تسجيل دفعة">
                    <i class="fas fa-money-bill"></i>
                </button>
                <button class="btn btn-info" onclick="viewCustomer('${customer.id}')" title="عرض التفاصيل">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-warning" onclick="sendPaymentReminder('${customer.id}')" title="تذكير بالدفع">
                    <i class="fas fa-bell"></i>
                </button>
            </td>
        `;
        tableBody.appendChild(row);
    }

    return tableContainer;
}

// إنشاء جدول المدفوعات الأخيرة
async function createRecentPaymentsTable() {
    const payments = (await db.getPayments()).slice(-10).reverse(); // آخر 10 مدفوعات
    
    const tableContainer = document.createElement('div');
    tableContainer.className = 'card';
    
    const header = document.createElement('div');
    header.className = 'card-header';
    header.innerHTML = `
        <h3 class="card-title">المدفوعات الأخيرة</h3>
        <div>
            <button class="btn btn-info" onclick="showAllPayments()">
                <i class="fas fa-list"></i> عرض الكل
            </button>
        </div>
    `;
    tableContainer.appendChild(header);
    
    if (payments.length === 0) {
        const emptyState = document.createElement('div');
        emptyState.className = 'empty-state';
        emptyState.innerHTML = `
            <i class="fas fa-receipt"></i>
            <h3>لا توجد مدفوعات</h3>
            <p>لم يتم تسجيل أي مدفوعات حتى الآن</p>
        `;
        tableContainer.appendChild(emptyState);
        return tableContainer;
    }
    
    const table = document.createElement('table');
    table.className = 'table';
    table.innerHTML = `
        <thead>
            <tr>
                <th>رقم الإيصال</th>
                <th>العميل</th>
                <th>المبلغ</th>
                <th>التاريخ</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody id="paymentsTableBody">
        </tbody>
    `;

    tableContainer.appendChild(table);

    // ملء الجدول بالبيانات
    const tableBody = table.querySelector('#paymentsTableBody');
    for (const payment of payments) {
        const customer = await db.getCustomerById(payment.customerId);
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${db.toArabicNumbers(payment.receiptNumber)}</td>
            <td>${customer ? customer.name : 'عميل محذوف'}</td>
            <td>${db.formatCurrencySync(payment.amount)}</td>
            <td>${db.formatDateTime(payment.createdAt)}</td>
            <td>
                <button class="btn btn-info" onclick="viewPayment('${payment.id}')" title="عرض">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-success" onclick="printPaymentReceipt('${payment.id}')" title="طباعة">
                    <i class="fas fa-print"></i>
                </button>
            </td>
        `;
        tableBody.appendChild(row);
    }

    return tableContainer;
}

// عرض جميع العملاء المدينين
function showAllDebtors() {
    app.navigateToPage('customers');
    
    // تطبيق فلتر العملاء المدينين
    setTimeout(() => {
        const balanceFilter = document.getElementById('balanceFilter');
        if (balanceFilter) {
            balanceFilter.value = 'positive';
            filterCustomers();
        }
    }, 100);
}

// عرض نافذة الدفعة السريعة
async function showQuickPayment() {
    const debtors = (await db.getCustomers()).filter(c => c.balance > 0 && c.id !== 'guest');
    
    if (debtors.length === 0) {
        app.showAlert('لا توجد ديون مستحقة', 'info');
        return;
    }
    
    const fields = [
        {
            name: 'customerId',
            label: 'العميل',
            type: 'select',
            required: true,
            options: debtors.map(customer => ({
                value: customer.id,
                text: `${customer.name} - ${db.formatCurrencySync(customer.balance)}`
            }))
        },
        {
            name: 'amount',
            label: 'مبلغ الدفعة',
            type: 'number',
            required: true,
            placeholder: '0.00',
            step: '0.01',
            min: '0'
        },
        {
            name: 'notes',
            label: 'ملاحظات',
            type: 'textarea',
            placeholder: 'ملاحظات الدفعة (اختياري)',
            rows: 3
        }
    ];
    
    const form = AppHelpers.createForm(fields, async (data) => {
        const customer = await db.getCustomerById(data.customerId);
        const amount = parseFloat(data.amount);
        
        if (amount <= 0) {
            app.showAlert('يجب أن يكون المبلغ أكبر من صفر', 'warning');
            return;
        }
        
        if (amount > customer.balance) {
            app.showAlert('المبلغ أكبر من الدين المستحق', 'warning');
            return;
        }
        
        const payment = {
            customerId: data.customerId,
            amount: amount,
            notes: data.notes.trim(),
            receiptNumber: generateReceiptNumber()
        };
        
        await db.savePayment(payment);
        
        app.showAlert('تم تسجيل الدفعة بنجاح', 'success');
        
        // طباعة إيصال الاستلام
        printPaymentReceipt(payment, customer);
        
        loadDebts();
    }, 'تسجيل الدفعة');
    
    const actions = [
        {
            text: 'إلغاء',
            type: 'secondary',
            handler: () => {}
        }
    ];
    
    AppHelpers.createModal('تسجيل دفعة سريعة', form, actions);
}

// عرض تفاصيل دفعة
async function viewPayment(paymentId) {
    const payments = await db.getPayments();
    const payment = payments.find(p => p.id === paymentId);
    if (!payment) return;

    const customer = await db.getCustomerById(payment.customerId);
    
    const content = `
        <div class="payment-details">
            <div class="info-grid">
                <div><strong>رقم الإيصال:</strong> ${db.toArabicNumbers(payment.receiptNumber)}</div>
                <div><strong>العميل:</strong> ${customer ? customer.name : 'عميل محذوف'}</div>
                <div><strong>المبلغ:</strong> ${db.formatCurrencySync(payment.amount)}</div>
                <div><strong>تاريخ الدفعة:</strong> ${db.formatDateTime(payment.createdAt)}</div>
                ${payment.notes ? `<div><strong>ملاحظات:</strong> ${payment.notes}</div>` : ''}
            </div>
        </div>
        
        <style>
            .payment-details .info-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 1rem;
            }
        </style>
    `;
    
    const actions = [
        {
            text: 'طباعة الإيصال',
            type: 'success',
            icon: 'fas fa-print',
            handler: () => printPaymentReceiptById(paymentId),
            close: false
        },
        {
            text: 'إغلاق',
            type: 'secondary',
            handler: () => {}
        }
    ];
    
    AppHelpers.createModal(`تفاصيل الدفعة: ${payment.receiptNumber}`, content, actions);
}

// طباعة إيصال الدفعة بالمعرف
function printPaymentReceiptById(paymentId) {
    const payment = db.getPayments().find(p => p.id === paymentId);
    const customer = db.getCustomerById(payment.customerId);
    printPaymentReceipt(payment, customer);
}

// إنشاء تقرير الديون
function generateDebtsReport() {
    const customers = db.getCustomers().filter(c => c.id !== 'guest');
    const debtors = customers.filter(c => c.balance > 0);
    const creditors = customers.filter(c => c.balance < 0);
    
    const totalDebts = debtors.reduce((sum, c) => sum + c.balance, 0);
    const totalCredits = Math.abs(creditors.reduce((sum, c) => sum + c.balance, 0));
    
    const content = `
        <h2 style="text-align: center; margin-bottom: 30px;">تقرير الديون والمدفوعات</h2>
        
        <div style="margin-bottom: 30px;">
            <h3>ملخص الديون</h3>
            <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px; background: #f5f5f5;"><strong>إجمالي الديون:</strong></td>
                    <td style="border: 1px solid #ddd; padding: 8px;">${db.formatCurrency(totalDebts)}</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px; background: #f5f5f5;"><strong>إجمالي الدائنين:</strong></td>
                    <td style="border: 1px solid #ddd; padding: 8px;">${db.formatCurrency(totalCredits)}</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px; background: #f5f5f5;"><strong>الرصيد الصافي:</strong></td>
                    <td style="border: 1px solid #ddd; padding: 8px;">${db.formatCurrency(totalDebts - totalCredits)}</td>
                </tr>
            </table>
        </div>
        
        ${debtors.length > 0 ? `
            <div style="margin-bottom: 30px;">
                <h3>العملاء المدينين</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background-color: #f5f5f5;">
                            <th style="border: 1px solid #ddd; padding: 8px;">اسم العميل</th>
                            <th style="border: 1px solid #ddd; padding: 8px;">الهاتف</th>
                            <th style="border: 1px solid #ddd; padding: 8px;">المبلغ المستحق</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${debtors.map(customer => `
                            <tr>
                                <td style="border: 1px solid #ddd; padding: 8px;">${customer.name}</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">${customer.phone || '-'}</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">${db.formatCurrency(customer.balance)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        ` : ''}
        
        <div style="margin-top: 30px; text-align: center;">
            <p><strong>تاريخ التقرير:</strong> ${db.formatDateTime(new Date())}</p>
        </div>
    `;
    
    app.printContent(content, 'تقرير الديون والمدفوعات');
}

// إرسال تذكير بالدفع (محاكاة)
function sendPaymentReminder(customerId) {
    const customer = db.getCustomerById(customerId);
    if (!customer) return;
    
    app.showAlert(`تم إرسال تذكير بالدفع للعميل ${customer.name}`, 'success');
}

// إرسال تذكيرات للجميع
function sendPaymentReminders() {
    const debtors = db.getCustomers().filter(c => c.balance > 0 && c.id !== 'guest');
    
    if (debtors.length === 0) {
        app.showAlert('لا توجد ديون مستحقة', 'info');
        return;
    }
    
    app.showConfirm(
        `هل تريد إرسال تذكيرات بالدفع لجميع العملاء المدينين (${db.toArabicNumbers(debtors.length)} عميل)؟`,
        () => {
            app.showAlert(`تم إرسال ${db.toArabicNumbers(debtors.length)} تذكير بالدفع`, 'success');
        },
        'تأكيد الإرسال'
    );
}

// عرض جميع المدفوعات
function showAllPayments() {
    const payments = db.getPayments();
    
    if (payments.length === 0) {
        app.showAlert('لا توجد مدفوعات', 'info');
        return;
    }
    
    const content = `
        <table class="table">
            <thead>
                <tr>
                    <th>رقم الإيصال</th>
                    <th>العميل</th>
                    <th>المبلغ</th>
                    <th>التاريخ</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                ${payments.reverse().map(payment => {
                    const customer = db.getCustomerById(payment.customerId);
                    return `
                        <tr>
                            <td>${db.toArabicNumbers(payment.receiptNumber)}</td>
                            <td>${customer ? customer.name : 'عميل محذوف'}</td>
                            <td>${db.formatCurrency(payment.amount)}</td>
                            <td>${db.formatDateTime(payment.createdAt)}</td>
                            <td>
                                <button class="btn btn-info" onclick="viewPayment('${payment.id}')">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-success" onclick="printPaymentReceiptById('${payment.id}')">
                                    <i class="fas fa-print"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                }).join('')}
            </tbody>
        </table>
    `;
    
    const actions = [
        {
            text: 'إغلاق',
            type: 'secondary',
            handler: () => {}
        }
    ];
    
    AppHelpers.createModal('جميع المدفوعات', content, actions);
}

// عرض نافذة الدفعات المجمعة
function showBulkPayment() {
    app.showAlert('سيتم تطوير هذه الميزة قريباً', 'info');
}
