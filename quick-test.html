<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع للأخطاء</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        #console-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار سريع للأخطاء النحوية</h1>
        
        <div class="info">
            هذه الصفحة لاختبار تحميل الملفات والتحقق من عدم وجود أخطاء نحوية
        </div>
        
        <button onclick="testFileLoading()">اختبار تحميل الملفات</button>
        <button onclick="testAsyncFunctions()">اختبار الدوال غير المتزامنة</button>
        <button onclick="clearConsole()">مسح وحدة التحكم</button>
        
        <div id="test-results"></div>
        
        <h3>مخرجات وحدة التحكم:</h3>
        <div id="console-output"></div>
    </div>

    <!-- تحميل الملفات بنفس ترتيب index.html -->
    <script src="database.js"></script>
    <script src="app.js"></script>
    <script src="main.js"></script>
    <script src="products.js"></script>
    <script src="sales.js"></script>
    <script src="customers.js"></script>
    <script src="suppliers.js"></script>
    <script src="purchases.js"></script>
    <script src="debts.js"></script>
    <script src="reports.js"></script>
    <script src="settings.js"></script>
    
    <script>
        // تسجيل جميع الأخطاء
        const originalConsoleError = console.error;
        const originalConsoleLog = console.log;
        const originalConsoleWarn = console.warn;
        
        const consoleOutput = document.getElementById('console-output');
        const testResults = document.getElementById('test-results');
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌ ERROR' : 
                          type === 'warn' ? '⚠️ WARN' : 
                          '📝 LOG';
            consoleOutput.textContent += `[${timestamp}] ${prefix}: ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.error = function(...args) {
            addToConsole(args.join(' '), 'error');
            originalConsoleError.apply(console, args);
        };
        
        console.log = function(...args) {
            addToConsole(args.join(' '), 'log');
            originalConsoleLog.apply(console, args);
        };
        
        console.warn = function(...args) {
            addToConsole(args.join(' '), 'warn');
            originalConsoleWarn.apply(console, args);
        };
        
        // تسجيل الأخطاء غير المعالجة
        window.addEventListener('error', (e) => {
            addToConsole(`Uncaught Error: ${e.message} at ${e.filename}:${e.lineno}`, 'error');
            addResult(`❌ خطأ نحوي: ${e.message}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', (e) => {
            addToConsole(`Unhandled Promise Rejection: ${e.reason}`, 'error');
            addResult(`❌ خطأ في Promise: ${e.reason}`, 'error');
        });
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = type;
            div.textContent = message;
            testResults.appendChild(div);
        }
        
        function clearConsole() {
            consoleOutput.textContent = '';
            testResults.innerHTML = '';
        }
        
        function testFileLoading() {
            addResult('🔄 اختبار تحميل الملفات...', 'info');
            
            const files = [
                'database.js',
                'app.js', 
                'main.js',
                'products.js',
                'sales.js',
                'customers.js',
                'suppliers.js',
                'purchases.js',
                'debts.js',
                'reports.js',
                'settings.js'
            ];
            
            files.forEach(file => {
                const scripts = document.querySelectorAll(`script[src="${file}"]`);
                if (scripts.length > 0) {
                    addResult(`✅ ${file} محمل`, 'success');
                } else {
                    addResult(`❌ ${file} غير محمل`, 'error');
                }
            });
        }
        
        async function testAsyncFunctions() {
            addResult('🔄 اختبار الدوال غير المتزامنة...', 'info');
            
            try {
                // اختبار وجود الدوال
                const functions = [
                    'loadPurchases',
                    'showPurchaseModal',
                    'savePurchase',
                    'loadCustomers',
                    'loadSuppliers'
                ];
                
                functions.forEach(funcName => {
                    if (typeof window[funcName] === 'function') {
                        addResult(`✅ دالة ${funcName} موجودة`, 'success');
                    } else {
                        addResult(`❌ دالة ${funcName} غير موجودة`, 'error');
                    }
                });
                
                // اختبار قاعدة البيانات
                if (typeof db !== 'undefined') {
                    addResult('✅ قاعدة البيانات متاحة', 'success');
                    
                    // اختبار دوال قاعدة البيانات
                    const suppliers = await db.getSuppliers();
                    addResult(`✅ تم جلب ${suppliers.length} موردين`, 'success');
                    
                    const products = await db.getProducts();
                    addResult(`✅ تم جلب ${products.length} منتجات`, 'success');
                    
                    const customers = await db.getCustomers();
                    addResult(`✅ تم جلب ${customers.length} عملاء`, 'success');
                    
                } else {
                    addResult('❌ قاعدة البيانات غير متاحة', 'error');
                }
                
                // اختبار التطبيق الرئيسي
                if (typeof App !== 'undefined') {
                    addResult('✅ فئة App متاحة', 'success');
                } else {
                    addResult('❌ فئة App غير متاحة', 'error');
                }
                
            } catch (error) {
                addResult(`❌ خطأ في الاختبار: ${error.message}`, 'error');
                console.error('Test error:', error);
            }
        }
        
        // تشغيل الاختبارات عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(() => {
                addResult('🚀 بدء الاختبارات التلقائية...', 'info');
                testFileLoading();
                
                setTimeout(() => {
                    testAsyncFunctions();
                }, 1000);
            }, 500);
        });
        
        console.log('🧪 صفحة الاختبار جاهزة');
    </script>
</body>
</html>
