# دليل بناء تطبيق BakryAfand POS

## المتطلبات الأساسية

### 1. Node.js
- **الإصدار المطلوب:** 16.0.0 أو أحدث
- **التحميل:** [nodejs.org](https://nodejs.org)
- **التحقق من التثبيت:**
```bash
node --version
npm --version
```

### 2. Git (اختياري)
- للحصول على الكود المصدري
- **التحميل:** [git-scm.com](https://git-scm.com)

## خطوات البناء

### الطريقة الأولى: استخدام ملف build.bat (Windows)

1. **افتح Command Prompt أو PowerShell**
2. **انتقل لمجلد المشروع:**
```bash
cd C:\Users\<USER>\Desktop\seles2
```

3. **شغل ملف البناء:**
```bash
build.bat
```

4. **اتبع التعليمات على الشاشة**

### الطريقة الثانية: الأوامر اليدوية

1. **تثبيت المتطلبات:**
```bash
npm install
```

2. **بناء التطبيق للويندوز:**
```bash
npm run build-win
```

3. **بناء لجميع المنصات:**
```bash
npm run build
```

4. **إنشاء نسخة محمولة:**
```bash
npm run pack
```

## أنواع الملفات المبنية

### Windows
- **مثبت NSIS:** `BakryAfand-POS-Setup-1.0.0.exe`
  - مثبت كامل مع إعدادات متقدمة
  - ينشئ اختصارات في قائمة ابدأ وسطح المكتب
  - يسجل التطبيق في النظام

- **نسخة محمولة:** `BakryAfand POS 1.0.0.exe`
  - لا تحتاج تثبيت
  - يمكن تشغيلها من أي مكان
  - مناسبة للاستخدام المؤقت

### macOS
- **ملف DMG:** `BakryAfand POS-1.0.0.dmg`
  - ملف تثبيت macOS التقليدي
  - يحتوي على التطبيق وتعليمات التثبيت

### Linux
- **AppImage:** `BakryAfand POS-1.0.0.AppImage`
  - ملف قابل للتشغيل مباشرة
  - لا يحتاج تثبيت

- **حزمة DEB:** `bakryafand-pos_1.0.0_amd64.deb`
  - للتوزيعات المبنية على Debian/Ubuntu

## مجلدات الإخراج

```
dist/
├── BakryAfand-POS-Setup-1.0.0.exe     # مثبت Windows
├── BakryAfand POS 1.0.0.exe           # نسخة محمولة Windows
├── BakryAfand POS-1.0.0.dmg           # مثبت macOS
├── BakryAfand POS-1.0.0.AppImage      # تطبيق Linux
└── bakryafand-pos_1.0.0_amd64.deb     # حزمة Debian
```

## التخصيص قبل البناء

### 1. تغيير الأيقونة
- **Windows:** استبدل `assets/icon.ico`
- **macOS:** استبدل `assets/icon.icns`
- **Linux:** استبدل `assets/icon.png`

### 2. تعديل معلومات التطبيق
في ملف `package.json`:
```json
{
  "name": "اسم-التطبيق",
  "version": "1.0.0",
  "description": "وصف التطبيق",
  "author": {
    "name": "اسم المطور",
    "email": "<EMAIL>"
  }
}
```

### 3. تخصيص المثبت
في ملف `installer.nsh`:
- تغيير رسائل المثبت
- إضافة خطوات تثبيت مخصصة
- تعديل إعدادات إلغاء التثبيت

## استكشاف الأخطاء

### خطأ: "node is not recognized"
**الحل:** تثبيت Node.js وإعادة تشغيل Command Prompt

### خطأ: "npm install failed"
**الحل:** 
```bash
npm cache clean --force
npm install
```

### خطأ: "electron-builder failed"
**الحل:**
```bash
npm install electron-builder --save-dev
npm run build
```

### خطأ: "icon not found"
**الحل:** إنشاء ملفات الأيقونات المطلوبة:
```bash
# إنشاء مجلد assets
mkdir assets

# إضافة ملفات الأيقونات
# icon.ico (Windows)
# icon.icns (macOS)  
# icon.png (Linux)
```

### خطأ: "Permission denied"
**الحل:** تشغيل Command Prompt كمدير (Run as Administrator)

## التحقق من البناء

### 1. اختبار الملف المبني
```bash
# تشغيل النسخة المحمولة
"dist/BakryAfand POS 1.0.0.exe"

# أو تثبيت المثبت واختباره
"dist/BakryAfand-POS-Setup-1.0.0.exe"
```

### 2. فحص حجم الملف
- **حجم طبيعي:** 100-200 MB
- **إذا كان أكبر:** تحقق من الملفات المضمنة في البناء

### 3. اختبار على أجهزة مختلفة
- Windows 10/11 (64-bit و 32-bit)
- أجهزة بدون Node.js مثبت
- أجهزة بمستويات صلاحيات مختلفة

## نصائح للتحسين

### 1. تقليل حجم الملف
في `package.json`:
```json
"build": {
  "files": [
    "**/*",
    "!test-*.html",
    "!*.md",
    "!node_modules/**/*"
  ]
}
```

### 2. تحسين الأداء
- ضغط الملفات قبل البناء
- إزالة الملفات غير المستخدمة
- تحسين الصور والأيقونات

### 3. الأمان
- فحص الملفات المبنية بمضاد الفيروسات
- توقيع الملفات رقمياً (للتوزيع التجاري)
- اختبار على بيئات مختلفة

## التوزيع

### 1. رفع الملفات
- **GitHub Releases:** للمشاريع مفتوحة المصدر
- **موقع ويب:** للتوزيع التجاري
- **متاجر التطبيقات:** Microsoft Store, Mac App Store

### 2. التوثيق
- دليل المستخدم
- متطلبات النظام
- تعليمات التثبيت

### 3. الدعم
- قناة للدعم التقني
- تحديثات دورية
- إصلاح الأخطاء

---

**ملاحظة:** هذا الدليل مخصص لبناء تطبيق BakryAfand POS. للحصول على مساعدة إضافية، راجع الوثائق الرسمية لـ Electron Builder.
