# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
out/

# Electron
app/
release/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# Test files
test-*.html
quick-test.html

# Documentation files (keep main README)
*.md
!README.md

# Backup files
*.bak
*.backup

# Temporary files
*.tmp
*.temp

# Package lock files (optional)
package-lock.json
yarn.lock

# Electron builder cache
.electron-builder-cache/

# Local data (for development)
data/
*.json.bak

# Windows specific
*.exe
*.msi
*.msm
*.msp

# macOS specific
*.dmg
*.pkg

# Linux specific
*.AppImage
*.deb
*.rpm

# Archives
*.zip
*.tar.gz
*.rar
*.7z
