const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');
const os = require('os');

// مسار مجلد البيانات
const userDataPath = path.join(os.homedir(), 'BakryAfand-POS-Data');

// إنشاء مجلد البيانات إذا لم يكن موجوداً
if (!fs.existsSync(userDataPath)) {
    fs.mkdirSync(userDataPath, { recursive: true });
}

let mainWindow;

function createWindow() {
    // إنشاء نافذة المتصفح
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        minWidth: 800,
        minHeight: 600,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true
        },
        icon: path.join(__dirname, 'assets/icon.png'),
        show: false,
        titleBarStyle: 'default'
    });

    // تحميل ملف index.html
    mainWindow.loadFile('index.html');

    // إظهار النافذة عند الانتهاء من التحميل
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
    });

    // فتح أدوات المطور في وضع التطوير
    if (process.env.NODE_ENV === 'development') {
        mainWindow.webContents.openDevTools();
    }
}

// تشغيل التطبيق عند الاستعداد
app.whenReady().then(createWindow);

// إنهاء التطبيق عند إغلاق جميع النوافذ
app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

// معالجات IPC للتعامل مع الملفات
ipcMain.handle('read-file', async (event, fileName) => {
    try {
        const filePath = path.join(userDataPath, fileName);
        if (fs.existsSync(filePath)) {
            const data = fs.readFileSync(filePath, 'utf8');
            return JSON.parse(data);
        }
        return null;
    } catch (error) {
        console.error('خطأ في قراءة الملف:', error);
        return null;
    }
});

ipcMain.handle('write-file', async (event, fileName, data) => {
    try {
        const filePath = path.join(userDataPath, fileName);
        fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
        return true;
    } catch (error) {
        console.error('خطأ في كتابة الملف:', error);
        return false;
    }
});

ipcMain.handle('file-exists', async (event, fileName) => {
    try {
        const filePath = path.join(userDataPath, fileName);
        return fs.existsSync(filePath);
    } catch (error) {
        console.error('خطأ في فحص وجود الملف:', error);
        return false;
    }
});

ipcMain.handle('delete-file', async (event, fileName) => {
    try {
        const filePath = path.join(userDataPath, fileName);
        if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
            return true;
        }
        return false;
    } catch (error) {
        console.error('خطأ في حذف الملف:', error);
        return false;
    }
});

ipcMain.handle('get-data-path', async () => {
    return userDataPath;
});

// معالج للنسخ الاحتياطي
ipcMain.handle('create-backup', async (event, backupData) => {
    try {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupFileName = `backup-${timestamp}.json`;
        const backupPath = path.join(userDataPath, 'backups');
        
        // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
        if (!fs.existsSync(backupPath)) {
            fs.mkdirSync(backupPath, { recursive: true });
        }
        
        const fullBackupPath = path.join(backupPath, backupFileName);
        fs.writeFileSync(fullBackupPath, JSON.stringify(backupData, null, 2), 'utf8');
        
        return {
            success: true,
            fileName: backupFileName,
            path: fullBackupPath
        };
    } catch (error) {
        console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
        return {
            success: false,
            error: error.message
        };
    }
});

// معالج لاستعادة النسخة الاحتياطية
ipcMain.handle('restore-backup', async (event, backupFileName) => {
    try {
        const backupPath = path.join(userDataPath, 'backups', backupFileName);
        if (fs.existsSync(backupPath)) {
            const data = fs.readFileSync(backupPath, 'utf8');
            return {
                success: true,
                data: JSON.parse(data)
            };
        }
        return {
            success: false,
            error: 'ملف النسخة الاحتياطية غير موجود'
        };
    } catch (error) {
        console.error('خطأ في استعادة النسخة الاحتياطية:', error);
        return {
            success: false,
            error: error.message
        };
    }
});

// معالج لقائمة النسخ الاحتياطية
ipcMain.handle('list-backups', async () => {
    try {
        const backupPath = path.join(userDataPath, 'backups');
        if (!fs.existsSync(backupPath)) {
            return [];
        }
        
        const files = fs.readdirSync(backupPath)
            .filter(file => file.endsWith('.json'))
            .map(file => {
                const filePath = path.join(backupPath, file);
                const stats = fs.statSync(filePath);
                return {
                    name: file,
                    size: stats.size,
                    created: stats.birthtime,
                    modified: stats.mtime
                };
            })
            .sort((a, b) => b.created - a.created);
        
        return files;
    } catch (error) {
        console.error('خطأ في قراءة قائمة النسخ الاحتياطية:', error);
        return [];
    }
});
