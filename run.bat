@echo off
chcp 65001 >nul
echo ========================================
echo    تشغيل BakryAfand POS (وضع التطوير)
echo ========================================
echo.

:: التحقق من وجود Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: Node.js غير مثبت على النظام
    echo يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

:: التحقق من وجود package.json
if not exist "package.json" (
    echo ❌ خطأ: ملف package.json غير موجود
    pause
    exit /b 1
)

:: التحقق من وجود node_modules
if not exist "node_modules" (
    echo 📦 تثبيت المتطلبات لأول مرة...
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ خطأ في تثبيت المتطلبات
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت المتطلبات بنجاح
    echo.
)

echo 🚀 تشغيل التطبيق...
echo.
echo ملاحظة: سيتم فتح التطبيق في نافذة Electron
echo لإغلاق التطبيق، أغلق النافذة أو اضغط Ctrl+C هنا
echo.

call npm start

echo.
echo تم إغلاق التطبيق
pause
