// إدارة المنتجات
async function loadProducts() {
    const pageContent = document.getElementById('pageContent');

    // إنشاء شريط الأدوات
    const toolbar = await createProductsToolbar();
    pageContent.appendChild(toolbar);

    // إنشاء جدول المنتجات
    const productsTable = await createProductsTable();
    pageContent.appendChild(productsTable);
}

// إنشاء شريط أدوات المنتجات
async function createProductsToolbar() {
    const toolbar = document.createElement('div');
    toolbar.className = 'card';
    toolbar.style.marginBottom = '1.5rem';
    
    const toolbarContent = document.createElement('div');
    toolbarContent.style.display = 'flex';
    toolbarContent.style.justifyContent = 'space-between';
    toolbarContent.style.alignItems = 'center';
    toolbarContent.style.flexWrap = 'wrap';
    toolbarContent.style.gap = '1rem';
    
    // الجانب الأيسر - البحث والفلترة
    const leftSide = document.createElement('div');
    leftSide.style.display = 'flex';
    leftSide.style.alignItems = 'center';
    leftSide.style.gap = '1rem';
    leftSide.style.flex = '1';
    
    // حقل البحث
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.placeholder = 'البحث في المنتجات...';
    searchInput.className = 'search-input';
    searchInput.style.maxWidth = '300px';
    searchInput.id = 'productSearch';
    
    // فلتر الفئات
    const categoryFilter = document.createElement('select');
    categoryFilter.className = 'filter-select';
    categoryFilter.id = 'categoryFilter';
    
    const categories = await getProductCategories();
    categoryFilter.innerHTML = `
        <option value="">جميع الفئات</option>
        ${categories.map(cat => `<option value="${cat}">${cat}</option>`).join('')}
    `;
    
    leftSide.appendChild(searchInput);
    leftSide.appendChild(categoryFilter);
    
    // الجانب الأيمن - الأزرار
    const rightSide = document.createElement('div');
    rightSide.style.display = 'flex';
    rightSide.style.alignItems = 'center';
    rightSide.style.gap = '0.5rem';
    
    // زر إضافة منتج جديد
    const addButton = document.createElement('button');
    addButton.className = 'btn btn-primary';
    addButton.innerHTML = '<i class="fas fa-plus"></i> إضافة منتج';
    addButton.addEventListener('click', () => showProductModal());
    
    // زر تصدير
    const exportButton = document.createElement('button');
    exportButton.className = 'btn btn-success';
    exportButton.innerHTML = '<i class="fas fa-download"></i> تصدير';
    exportButton.addEventListener('click', exportProducts);
    
    // زر طباعة
    const printButton = document.createElement('button');
    printButton.className = 'btn btn-info';
    printButton.innerHTML = '<i class="fas fa-print"></i> طباعة';
    printButton.addEventListener('click', printProducts);
    
    rightSide.appendChild(addButton);
    rightSide.appendChild(exportButton);
    rightSide.appendChild(printButton);
    
    toolbarContent.appendChild(leftSide);
    toolbarContent.appendChild(rightSide);
    toolbar.appendChild(toolbarContent);
    
    // إضافة مستمعي الأحداث للبحث والفلترة
    searchInput.addEventListener('input', filterProducts);
    categoryFilter.addEventListener('change', filterProducts);
    
    return toolbar;
}

// إنشاء جدول المنتجات
async function createProductsTable() {
    const products = await db.getProducts();
    
    const tableContainer = document.createElement('div');
    tableContainer.className = 'card';
    
    if (products.length === 0) {
        tableContainer.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-box"></i>
                <h3>لا توجد منتجات</h3>
                <p>ابدأ بإضافة منتجات جديدة لمتجرك</p>
                <button class="btn btn-primary" onclick="showProductModal()">
                    <i class="fas fa-plus"></i> إضافة أول منتج
                </button>
            </div>
        `;
        return tableContainer;
    }
    
    const table = document.createElement('table');
    table.className = 'table';
    table.id = 'productsTable';
    
    // رأس الجدول
    table.innerHTML = `
        <thead>
            <tr>
                <th>الصورة</th>
                <th>اسم المنتج</th>
                <th>الفئة</th>
                <th>السعر</th>
                <th>الكمية</th>
                <th>الحالة</th>
                <th>تاريخ الإضافة</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            ${products.map(product => createProductRow(product)).join('')}
        </tbody>
    `;
    
    tableContainer.appendChild(table);
    return tableContainer;
}

// إنشاء صف منتج
function createProductRow(product) {
    const settings = db.getSettings();
    const isLowStock = product.quantity <= settings.lowStockAlert;
    
    return `
        <tr data-product-id="${product.id}">
            <td>
                <div class="product-image">
                    ${product.image ? 
                        `<img src="${product.image}" alt="${product.name}" style="width: 50px; height: 50px; object-fit: cover; border-radius: 8px;">` :
                        `<div style="width: 50px; height: 50px; background: #f0f0f0; border-radius: 8px; display: flex; align-items: center; justify-content: center;"><i class="fas fa-image" style="color: #ccc;"></i></div>`
                    }
                </div>
            </td>
            <td>
                <div>
                    <strong>${product.name}</strong>
                    ${product.description ? `<br><small style="color: #666;">${product.description}</small>` : ''}
                </div>
            </td>
            <td>${product.category || 'غير محدد'}</td>
            <td>${db.formatCurrency(product.price)}</td>
            <td>
                <span class="badge ${isLowStock ? 'badge-danger' : 'badge-success'}">
                    ${db.toArabicNumbers(product.quantity)}
                </span>
            </td>
            <td>
                <span class="badge ${product.quantity > 0 ? 'badge-success' : 'badge-danger'}">
                    ${product.quantity > 0 ? 'متوفر' : 'نفد المخزون'}
                </span>
            </td>
            <td>${db.formatDate(product.createdAt)}</td>
            <td>
                <button class="btn btn-warning" onclick="editProduct('${product.id}')" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-danger" onclick="deleteProduct('${product.id}')" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
                <button class="btn btn-info" onclick="viewProduct('${product.id}')" title="عرض">
                    <i class="fas fa-eye"></i>
                </button>
            </td>
        </tr>
    `;
}

// الحصول على فئات المنتجات
async function getProductCategories() {
    const products = await db.getProducts();
    const categories = [...new Set(products.map(p => p.category).filter(c => c))];
    return categories.sort();
}

// فلترة المنتجات
function filterProducts() {
    const searchTerm = document.getElementById('productSearch').value.toLowerCase();
    const selectedCategory = document.getElementById('categoryFilter').value;
    const rows = document.querySelectorAll('#productsTable tbody tr');
    
    rows.forEach(row => {
        const productName = row.cells[1].textContent.toLowerCase();
        const productCategory = row.cells[2].textContent;
        
        const matchesSearch = productName.includes(searchTerm);
        const matchesCategory = !selectedCategory || productCategory === selectedCategory;
        
        row.style.display = matchesSearch && matchesCategory ? '' : 'none';
    });
}

// عرض نافذة إضافة/تعديل منتج
function showProductModal(productId = null) {
    const isEdit = !!productId;
    const product = isEdit ? db.getProductById(productId) : {};
    
    const fields = [
        {
            name: 'name',
            label: 'اسم المنتج',
            type: 'text',
            required: true,
            value: product.name || '',
            placeholder: 'أدخل اسم المنتج'
        },
        {
            name: 'description',
            label: 'الوصف',
            type: 'textarea',
            value: product.description || '',
            placeholder: 'وصف المنتج (اختياري)',
            rows: 3
        },
        {
            name: 'category',
            label: 'الفئة',
            type: 'text',
            value: product.category || '',
            placeholder: 'فئة المنتج'
        },
        {
            name: 'price',
            label: 'السعر',
            type: 'number',
            required: true,
            value: product.price || '',
            placeholder: '0.00'
        },
        {
            name: 'quantity',
            label: 'الكمية',
            type: 'number',
            required: true,
            value: product.quantity || '',
            placeholder: '0'
        },
        {
            name: 'barcode',
            label: 'الباركود',
            type: 'text',
            value: product.barcode || '',
            placeholder: 'رقم الباركود (اختياري)'
        }
    ];
    
    const form = AppHelpers.createForm(fields, (data) => {
        saveProduct(data, productId);
    }, isEdit ? 'تحديث' : 'إضافة');
    
    const actions = [
        {
            text: 'إلغاء',
            type: 'secondary',
            handler: () => {}
        }
    ];
    
    AppHelpers.createModal(
        isEdit ? 'تعديل المنتج' : 'إضافة منتج جديد',
        form,
        actions
    );
}

// حفظ المنتج
function saveProduct(data, productId = null) {
    try {
        const productData = {
            name: data.name.trim(),
            description: data.description.trim(),
            category: data.category.trim(),
            price: parseFloat(data.price),
            quantity: parseInt(data.quantity),
            barcode: data.barcode.trim()
        };
        
        if (productId) {
            productData.id = productId;
        }
        
        db.saveProduct(productData);
        
        app.showAlert(
            productId ? 'تم تحديث المنتج بنجاح' : 'تم إضافة المنتج بنجاح',
            'success'
        );
        
        loadProducts(); // إعادة تحميل الصفحة
    } catch (error) {
        app.showAlert('حدث خطأ أثناء حفظ المنتج', 'danger');
    }
}

// تعديل منتج
function editProduct(productId) {
    showProductModal(productId);
}

// حذف منتج
function deleteProduct(productId) {
    const product = db.getProductById(productId);
    if (!product) return;
    
    app.showConfirm(
        `هل أنت متأكد من حذف المنتج "${product.name}"؟`,
        () => {
            db.deleteProduct(productId);
            app.showAlert('تم حذف المنتج بنجاح', 'success');
            loadProducts();
        },
        'تأكيد الحذف'
    );
}

// عرض تفاصيل المنتج
function viewProduct(productId) {
    const product = db.getProductById(productId);
    if (!product) return;
    
    const content = `
        <div class="product-details">
            <div class="row">
                <div class="col">
                    <strong>اسم المنتج:</strong> ${product.name}
                </div>
            </div>
            ${product.description ? `
                <div class="row">
                    <div class="col">
                        <strong>الوصف:</strong> ${product.description}
                    </div>
                </div>
            ` : ''}
            <div class="row">
                <div class="col">
                    <strong>الفئة:</strong> ${product.category || 'غير محدد'}
                </div>
                <div class="col">
                    <strong>السعر:</strong> ${db.formatCurrency(product.price)}
                </div>
            </div>
            <div class="row">
                <div class="col">
                    <strong>الكمية المتاحة:</strong> ${db.toArabicNumbers(product.quantity)}
                </div>
                <div class="col">
                    <strong>الباركود:</strong> ${product.barcode || 'غير محدد'}
                </div>
            </div>
            <div class="row">
                <div class="col">
                    <strong>تاريخ الإضافة:</strong> ${db.formatDateTime(product.createdAt)}
                </div>
                ${product.updatedAt ? `
                    <div class="col">
                        <strong>آخر تحديث:</strong> ${db.formatDateTime(product.updatedAt)}
                    </div>
                ` : ''}
            </div>
        </div>
        <style>
            .product-details .row {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 1rem;
                margin-bottom: 1rem;
                padding: 0.5rem 0;
                border-bottom: 1px solid #eee;
            }
            .product-details .row:last-child {
                border-bottom: none;
            }
            .product-details .col {
                padding: 0.25rem 0;
            }
        </style>
    `;
    
    const actions = [
        {
            text: 'تعديل',
            type: 'warning',
            icon: 'fas fa-edit',
            handler: () => editProduct(productId),
            close: false
        },
        {
            text: 'إغلاق',
            type: 'secondary',
            handler: () => {}
        }
    ];
    
    AppHelpers.createModal(`تفاصيل المنتج: ${product.name}`, content, actions);
}

// تصدير المنتجات
function exportProducts() {
    const products = db.getProducts();
    
    if (products.length === 0) {
        app.showAlert('لا توجد منتجات للتصدير', 'warning');
        return;
    }
    
    const csvData = [
        ['اسم المنتج', 'الوصف', 'الفئة', 'السعر', 'الكمية', 'الباركود', 'تاريخ الإضافة']
    ];
    
    products.forEach(product => {
        csvData.push([
            product.name,
            product.description || '',
            product.category || '',
            product.price,
            product.quantity,
            product.barcode || '',
            db.formatDate(product.createdAt)
        ]);
    });
    
    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `products_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        app.showAlert('تم تصدير المنتجات بنجاح', 'success');
    }
}

// طباعة المنتجات
function printProducts() {
    const products = db.getProducts();
    
    if (products.length === 0) {
        app.showAlert('لا توجد منتجات للطباعة', 'warning');
        return;
    }
    
    const content = `
        <h2 style="text-align: center; margin-bottom: 30px;">قائمة المنتجات</h2>
        <table style="width: 100%; border-collapse: collapse;">
            <thead>
                <tr style="background-color: #f5f5f5;">
                    <th style="border: 1px solid #ddd; padding: 8px;">اسم المنتج</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">الفئة</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">السعر</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">الكمية</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">الحالة</th>
                </tr>
            </thead>
            <tbody>
                ${products.map(product => `
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px;">${product.name}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${product.category || 'غير محدد'}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${db.formatCurrency(product.price)}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${db.toArabicNumbers(product.quantity)}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${product.quantity > 0 ? 'متوفر' : 'نفد المخزون'}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
        <div style="margin-top: 30px; text-align: center;">
            <p><strong>إجمالي المنتجات:</strong> ${db.toArabicNumbers(products.length)} منتج</p>
            <p><strong>المنتجات المتوفرة:</strong> ${db.toArabicNumbers(products.filter(p => p.quantity > 0).length)} منتج</p>
        </div>
    `;
    
    app.printContent(content, 'قائمة المنتجات');
}
