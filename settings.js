// إعدادات النظام
async function loadSettings() {
    const pageContent = document.getElementById('pageContent');

    // إضافة تنبيه المنطقة المحمية
    const securityAlert = document.createElement('div');
    securityAlert.className = 'security-alert';
    securityAlert.innerHTML = `
        <div class="alert alert-warning" style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">
            <i class="fas fa-shield-alt" style="font-size: 1.5rem;"></i>
            <div>
                <strong>منطقة محمية</strong>
                <p style="margin: 0; font-size: 0.9rem;">أنت الآن في منطقة الإعدادات المحمية. يرجى الحذر عند إجراء أي تغييرات.</p>
            </div>
        </div>
    `;
    pageContent.appendChild(securityAlert);

    // إنشاء تخطيط الإعدادات
    const settingsLayout = document.createElement('div');
    settingsLayout.style.display = 'grid';
    settingsLayout.style.gridTemplateColumns = '1fr 1fr';
    settingsLayout.style.gap = '1.5rem';

    // بطاقة معلومات الشركة
    const companyCard = await createCompanySettingsCard();
    settingsLayout.appendChild(companyCard);

    // بطاقة إعدادات النظام
    const systemCard = await createSystemSettingsCard();
    settingsLayout.appendChild(systemCard);
    
    pageContent.appendChild(settingsLayout);
    
    // إنشاء صف ثاني
    const secondRow = document.createElement('div');
    secondRow.style.display = 'grid';
    secondRow.style.gridTemplateColumns = '1fr 1fr';
    secondRow.style.gap = '1.5rem';
    secondRow.style.marginTop = '1.5rem';
    
    // بطاقة الأمان
    const securityCard = createSecuritySettingsCard();
    secondRow.appendChild(securityCard);
    
    // بطاقة النسخ الاحتياطي
    const backupCard = createBackupSettingsCard();
    secondRow.appendChild(backupCard);
    
    pageContent.appendChild(secondRow);
}

// إنشاء بطاقة معلومات الشركة
async function createCompanySettingsCard() {
    const settings = await db.getSettings();
    
    const content = `
        <form id="companyForm" class="settings-form">
            <div class="form-group">
                <label>اسم الشركة</label>
                <input type="text" id="companyName" value="${settings.companyName}" required>
            </div>
            <div class="form-group">
                <label>عنوان الشركة</label>
                <textarea id="companyAddress" rows="3" required>${settings.companyAddress}</textarea>
            </div>
            <div class="form-group">
                <label>رقم الهاتف</label>
                <input type="tel" id="companyPhone" value="${settings.companyPhone}" required>
            </div>
            <div class="form-group">
                <label>البريد الإلكتروني</label>
                <input type="email" id="companyEmail" value="${settings.companyEmail}" required>
            </div>
            <div class="form-group">
                <label>العملة</label>
                <select id="currency">
                    <option value="ر.ع" ${settings.currency === 'ر.ع' ? 'selected' : ''}>ريال عماني (ر.ع)</option>
                    <option value="ر.س" ${settings.currency === 'ر.س' ? 'selected' : ''}>ريال سعودي (ر.س)</option>
                    <option value="د.إ" ${settings.currency === 'د.إ' ? 'selected' : ''}>درهم إماراتي (د.إ)</option>
                    <option value="د.ك" ${settings.currency === 'د.ك' ? 'selected' : ''}>دينار كويتي (د.ك)</option>
                    <option value="د.ب" ${settings.currency === 'د.ب' ? 'selected' : ''}>دينار بحريني (د.ب)</option>
                    <option value="ر.ق" ${settings.currency === 'ر.ق' ? 'selected' : ''}>ريال قطري (ر.ق)</option>
                    <option value="ج.م" ${settings.currency === 'ج.م' ? 'selected' : ''}>جنيه مصري (ج.م)</option>
                    <option value="د.أ" ${settings.currency === 'د.أ' ? 'selected' : ''}>دينار أردني (د.أ)</option>
                    <option value="ل.ل" ${settings.currency === 'ل.ل' ? 'selected' : ''}>ليرة لبنانية (ل.ل)</option>
                    <option value="$" ${settings.currency === '$' ? 'selected' : ''}>دولار أمريكي ($)</option>
                    <option value="€" ${settings.currency === '€' ? 'selected' : ''}>يورو (€)</option>
                </select>
            </div>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> حفظ معلومات الشركة
            </button>
        </form>
        
        <style>
            .settings-form .form-group {
                margin-bottom: 1rem;
            }
            .settings-form label {
                display: block;
                margin-bottom: 0.5rem;
                font-weight: 500;
                color: var(--text-primary);
            }
            .settings-form input,
            .settings-form textarea,
            .settings-form select {
                width: 100%;
                padding: 0.75rem;
                border: 2px solid #e0e0e0;
                border-radius: var(--border-radius);
                font-size: 1rem;
                font-family: inherit;
                background: var(--bg-secondary);
                transition: all 0.3s ease;
            }
            .settings-form input:focus,
            .settings-form textarea:focus,
            .settings-form select:focus {
                outline: none;
                border-color: var(--primary-color);
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            }
        </style>
    `;
    
    const card = AppHelpers.createCard('معلومات الشركة', content);
    
    // إضافة مستمع الحدث للنموذج
    setTimeout(() => {
        document.getElementById('companyForm').addEventListener('submit', saveCompanySettings);
    }, 100);
    
    return card;
}

// إنشاء بطاقة إعدادات النظام
async function createSystemSettingsCard() {
    const settings = await db.getSettings();
    
    const content = `
        <form id="systemForm" class="settings-form">
            <div class="form-group">
                <label>نسبة الضريبة (%)</label>
                <input type="number" id="taxRate" value="${settings.taxRate}" min="0" max="100" step="0.1" required>
            </div>
            <div class="form-group">
                <label>تنبيه المخزون المنخفض</label>
                <input type="number" id="lowStockAlert" value="${settings.lowStockAlert}" min="1" required>
                <small>سيتم التنبيه عندما تصل كمية المنتج إلى هذا الرقم أو أقل</small>
            </div>
            <div class="form-group">
                <label>المظهر</label>
                <select id="theme">
                    <option value="light" ${settings.theme === 'light' ? 'selected' : ''}>فاتح</option>
                    <option value="dark" ${settings.theme === 'dark' ? 'selected' : ''}>داكن</option>
                </select>
            </div>
            <div class="form-group">
                <label class="checkbox-label">
                    <input type="checkbox" id="autoBackup" ${settings.autoBackup ? 'checked' : ''}>
                    <span class="checkmark"></span>
                    تفعيل النسخ الاحتياطي التلقائي
                </label>
                <small>سيتم إنشاء نسخة احتياطية تلقائياً كل يوم</small>
            </div>
            <button type="submit" class="btn btn-success">
                <i class="fas fa-cog"></i> حفظ إعدادات النظام
            </button>
        </form>
        
        <style>
            .checkbox-label {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                cursor: pointer;
                font-weight: normal !important;
            }
            .checkbox-label input[type="checkbox"] {
                width: auto;
                margin: 0;
            }
            .settings-form small {
                display: block;
                margin-top: 0.25rem;
                color: var(--text-secondary);
                font-size: 0.8rem;
            }
        </style>
    `;
    
    const card = AppHelpers.createCard('إعدادات النظام', content);
    
    // إضافة مستمع الحدث للنموذج
    setTimeout(() => {
        document.getElementById('systemForm').addEventListener('submit', saveSystemSettings);
    }, 100);
    
    return card;
}

// إنشاء بطاقة إعدادات الأمان
function createSecuritySettingsCard() {
    const content = `
        <form id="securityForm" class="settings-form">
            <div class="form-group">
                <label>كلمة المرور الحالية</label>
                <input type="password" id="currentPassword" placeholder="أدخل كلمة المرور الحالية" required>
            </div>
            <div class="form-group">
                <label>كلمة المرور الجديدة</label>
                <input type="password" id="newPassword" placeholder="أدخل كلمة المرور الجديدة" required>
            </div>
            <div class="form-group">
                <label>تأكيد كلمة المرور الجديدة</label>
                <input type="password" id="confirmPassword" placeholder="أعد إدخال كلمة المرور الجديدة" required>
            </div>
            <div class="password-strength" id="passwordStrength" style="display: none;">
                <div class="strength-bar">
                    <div class="strength-fill"></div>
                </div>
                <span class="strength-text"></span>
            </div>
            <button type="submit" class="btn btn-warning">
                <i class="fas fa-key"></i> تغيير كلمة المرور
            </button>
        </form>
        
        <div class="security-info">
            <h4>نصائح الأمان</h4>
            <ul>
                <li>استخدم كلمة مرور قوية تحتوي على أحرف وأرقام</li>
                <li>لا تشارك كلمة المرور مع أي شخص</li>
                <li>قم بتغيير كلمة المرور بانتظام</li>
                <li>احتفظ بنسخة احتياطية من البيانات</li>
            </ul>
        </div>
        
        <style>
            .password-strength {
                margin: 0.5rem 0;
            }
            .strength-bar {
                width: 100%;
                height: 6px;
                background: #e0e0e0;
                border-radius: 3px;
                overflow: hidden;
                margin-bottom: 0.25rem;
            }
            .strength-fill {
                height: 100%;
                transition: all 0.3s ease;
                border-radius: 3px;
            }
            .strength-text {
                font-size: 0.8rem;
                font-weight: 500;
            }
            .security-info {
                margin-top: 2rem;
                padding: 1rem;
                background: #f8f9fa;
                border-radius: var(--border-radius);
                border-right: 4px solid var(--warning-color);
            }
            .security-info h4 {
                margin: 0 0 1rem 0;
                color: var(--warning-color);
            }
            .security-info ul {
                margin: 0;
                padding-right: 1.5rem;
            }
            .security-info li {
                margin-bottom: 0.5rem;
                color: var(--text-secondary);
                font-size: 0.9rem;
            }
        </style>
    `;
    
    const card = AppHelpers.createCard('الأمان وكلمة المرور', content);
    
    // إضافة مستمعي الأحداث
    setTimeout(() => {
        document.getElementById('securityForm').addEventListener('submit', changePassword);
        document.getElementById('newPassword').addEventListener('input', checkPasswordStrength);
    }, 100);
    
    return card;
}

// إنشاء بطاقة النسخ الاحتياطي
function createBackupSettingsCard() {
    const content = `
        <div class="backup-actions">
            <div class="backup-action">
                <div class="action-info">
                    <h4>تصدير البيانات</h4>
                    <p>إنشاء نسخة احتياطية من جميع البيانات</p>
                </div>
                <button class="btn btn-success" onclick="exportAllData()">
                    <i class="fas fa-download"></i> تصدير
                </button>
            </div>
            
            <div class="backup-action">
                <div class="action-info">
                    <h4>استيراد البيانات</h4>
                    <p>استعادة البيانات من نسخة احتياطية</p>
                </div>
                <div class="import-controls">
                    <input type="file" id="importFile" accept=".json" style="display: none;">
                    <button class="btn btn-info" onclick="document.getElementById('importFile').click()">
                        <i class="fas fa-upload"></i> اختيار ملف
                    </button>
                </div>
            </div>
            
            <div class="backup-action">
                <div class="action-info">
                    <h4>حذف جميع البيانات وإعادة تعيين النظام</h4>
                    <p>حذف جميع البيانات والإعدادات والنسخ الاحتياطية نهائياً</p>
                    <small class="text-danger">⚠️ تحذير: سيتم حذف جميع البيانات بما في ذلك البيانات الافتراضية - لا يمكن التراجع عنه!</small>
                </div>
                <button class="btn btn-danger" onclick="handleClearAllData()">
                    <i class="fas fa-trash-alt"></i> حذف جميع البيانات
                </button>
            </div>

            <div class="backup-action">
                <div class="action-info">
                    <h4>إعادة تهيئة النظام بالبيانات الافتراضية</h4>
                    <p>إعادة إنشاء البيانات الافتراضية (منتجات وعملاء نموذجيين)</p>
                    <small class="text-muted">يستخدم فقط إذا كان النظام فارغاً وتريد العودة للبيانات النموذجية</small>
                </div>
                <button class="btn btn-success" onclick="handleResetToDefaults()">
                    <i class="fas fa-redo"></i> إعادة تهيئة النظام
                </button>
            </div>
        </div>
        
        <div class="backup-info">
            <h4>معلومات النسخ الاحتياطي</h4>
            <div class="info-item">
                <span>آخر نسخة احتياطية:</span>
                <span id="lastBackup">لم يتم إنشاء نسخة احتياطية</span>
            </div>
            <div class="info-item">
                <span>حجم البيانات:</span>
                <span id="dataSize">${calculateDataSize()}</span>
            </div>
        </div>
        
        <style>
            .backup-actions {
                display: flex;
                flex-direction: column;
                gap: 1rem;
                margin-bottom: 2rem;
            }
            .backup-action {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 1rem;
                background: #f8f9fa;
                border-radius: var(--border-radius);
                border-right: 4px solid var(--primary-color);
            }
            .action-info h4 {
                margin: 0 0 0.25rem 0;
                color: var(--text-primary);
            }
            .action-info p {
                margin: 0;
                color: var(--text-secondary);
                font-size: 0.9rem;
            }
            .backup-info {
                padding: 1rem;
                background: #e3f2fd;
                border-radius: var(--border-radius);
                border-right: 4px solid var(--info-color);
            }
            .backup-info h4 {
                margin: 0 0 1rem 0;
                color: var(--info-color);
            }
            .info-item {
                display: flex;
                justify-content: space-between;
                margin-bottom: 0.5rem;
                font-size: 0.9rem;
            }
            .info-item:last-child {
                margin-bottom: 0;
            }
        </style>
    `;
    
    const card = AppHelpers.createCard('النسخ الاحتياطي والاستعادة', content);
    
    // إضافة مستمع الحدث لاستيراد الملف
    setTimeout(() => {
        document.getElementById('importFile').addEventListener('change', importDataFile);
        updateLastBackupInfo();
    }, 100);
    
    return card;
}

// حفظ إعدادات الشركة
async function saveCompanySettings(e) {
    e.preventDefault();

    try {
        const settings = await db.getSettings();
        settings.companyName = document.getElementById('companyName').value.trim();
        settings.companyAddress = document.getElementById('companyAddress').value.trim();
        settings.companyPhone = document.getElementById('companyPhone').value.trim();
        settings.companyEmail = document.getElementById('companyEmail').value.trim();
        settings.currency = document.getElementById('currency').value;

        const success = await db.saveSettings(settings);
        if (success) {
            app.showAlert('تم حفظ معلومات الشركة بنجاح', 'success');
        } else {
            app.showAlert('حدث خطأ أثناء حفظ معلومات الشركة', 'danger');
        }
    } catch (error) {
        console.error('خطأ في حفظ معلومات الشركة:', error);
        app.showAlert('حدث خطأ أثناء حفظ معلومات الشركة', 'danger');
    }
}

// حفظ إعدادات النظام
async function saveSystemSettings(e) {
    e.preventDefault();

    try {
        const settings = await db.getSettings();
        settings.taxRate = parseFloat(document.getElementById('taxRate').value);
        settings.lowStockAlert = parseInt(document.getElementById('lowStockAlert').value);
        settings.theme = document.getElementById('theme').value;
        settings.autoBackup = document.getElementById('autoBackup').checked;

        const success = await db.saveSettings(settings);
        if (success) {
            app.showAlert('تم حفظ إعدادات النظام بنجاح', 'success');

            // تطبيق المظهر الجديد
            applyTheme(settings.theme);
        } else {
            app.showAlert('حدث خطأ أثناء حفظ إعدادات النظام', 'danger');
        }
    } catch (error) {
        console.error('خطأ في حفظ إعدادات النظام:', error);
        app.showAlert('حدث خطأ أثناء حفظ إعدادات النظام', 'danger');
    }
}

// تغيير كلمة المرور
function changePassword(e) {
    e.preventDefault();
    
    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    
    // التحقق من كلمة المرور الحالية
    if (!db.verifyPassword(currentPassword)) {
        app.showAlert('كلمة المرور الحالية غير صحيحة', 'danger');
        return;
    }
    
    // التحقق من تطابق كلمة المرور الجديدة
    if (newPassword !== confirmPassword) {
        app.showAlert('كلمة المرور الجديدة غير متطابقة', 'danger');
        return;
    }
    
    // التحقق من قوة كلمة المرور
    if (newPassword.length < 3) {
        app.showAlert('كلمة المرور يجب أن تكون 3 أحرف على الأقل', 'warning');
        return;
    }
    
    // تغيير كلمة المرور
    db.changePassword(newPassword);
    app.showAlert('تم تغيير كلمة المرور بنجاح', 'success');
    
    // مسح الحقول
    document.getElementById('currentPassword').value = '';
    document.getElementById('newPassword').value = '';
    document.getElementById('confirmPassword').value = '';
    document.getElementById('passwordStrength').style.display = 'none';
}

// فحص قوة كلمة المرور
function checkPasswordStrength() {
    const password = document.getElementById('newPassword').value;
    const strengthElement = document.getElementById('passwordStrength');
    const fillElement = strengthElement.querySelector('.strength-fill');
    const textElement = strengthElement.querySelector('.strength-text');
    
    if (password.length === 0) {
        strengthElement.style.display = 'none';
        return;
    }
    
    strengthElement.style.display = 'block';
    
    let strength = 0;
    let strengthText = '';
    let strengthColor = '';
    
    // طول كلمة المرور
    if (password.length >= 8) strength += 25;
    else if (password.length >= 6) strength += 15;
    else if (password.length >= 4) strength += 10;
    
    // وجود أحرف كبيرة وصغيرة
    if (/[a-z]/.test(password) && /[A-Z]/.test(password)) strength += 25;
    
    // وجود أرقام
    if (/\d/.test(password)) strength += 25;
    
    // وجود رموز خاصة
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength += 25;
    
    if (strength < 25) {
        strengthText = 'ضعيفة';
        strengthColor = '#f44336';
    } else if (strength < 50) {
        strengthText = 'متوسطة';
        strengthColor = '#ff9800';
    } else if (strength < 75) {
        strengthText = 'جيدة';
        strengthColor = '#2196f3';
    } else {
        strengthText = 'قوية';
        strengthColor = '#4caf50';
    }
    
    fillElement.style.width = strength + '%';
    fillElement.style.backgroundColor = strengthColor;
    textElement.textContent = strengthText;
    textElement.style.color = strengthColor;
}

// تطبيق المظهر
function applyTheme(theme) {
    // سيتم تطوير هذه الوظيفة لاحقاً
    console.log('تطبيق المظهر:', theme);
}

// تصدير جميع البيانات
function exportAllData() {
    try {
        const data = db.exportData();
        const blob = new Blob([data], { type: 'application/json' });
        const link = document.createElement('a');
        
        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `backup_${new Date().toISOString().split('T')[0]}.json`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // حفظ تاريخ آخر نسخة احتياطية
            localStorage.setItem('lastBackup', new Date().toISOString());
            updateLastBackupInfo();
            
            app.showAlert('تم تصدير البيانات بنجاح', 'success');
        }
    } catch (error) {
        app.showAlert('حدث خطأ أثناء تصدير البيانات', 'danger');
    }
}

// استيراد ملف البيانات
function importDataFile(e) {
    const file = e.target.files[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = function(event) {
        try {
            const success = db.importData(event.target.result);
            if (success) {
                app.showAlert('تم استيراد البيانات بنجاح', 'success');
                setTimeout(() => {
                    location.reload();
                }, 2000);
            } else {
                app.showAlert('فشل في استيراد البيانات - تأكد من صحة الملف', 'danger');
            }
        } catch (error) {
            app.showAlert('ملف غير صالح', 'danger');
        }
    };
    reader.readAsText(file);
}

// حذف جميع البيانات نهائياً
async function clearAllData() {
    app.showConfirm(
        '⚠️ تحذير شديد ⚠️\n\nهل أنت متأكد من حذف جميع البيانات نهائياً؟\n\n• سيتم حذف جميع المنتجات والعملاء والموردين\n• سيتم حذف جميع المبيعات والمشتريات والمدفوعات\n• سيتم حذف جميع الإعدادات\n• سيتم حذف جميع النسخ الاحتياطية\n• لن يتم إعادة إنشاء أي بيانات افتراضية\n• هذا الإجراء لا يمكن التراجع عنه نهائياً!\n\nاكتب "حذف نهائي" للتأكيد:',
        async () => {
            // طلب تأكيد إضافي
            const confirmation = prompt('للتأكيد، اكتب "حذف نهائي" بالضبط:');
            if (confirmation === 'حذف نهائي') {
                try {
                    const success = await db.clearAllData();
                    if (success) {
                        app.showAlert('تم حذف جميع البيانات نهائياً', 'success');
                        setTimeout(() => {
                            location.reload();
                        }, 2000);
                    } else {
                        app.showAlert('حدث خطأ أثناء حذف البيانات', 'danger');
                    }
                } catch (error) {
                    console.error('خطأ في حذف البيانات نهائياً:', error);
                    app.showAlert('حدث خطأ أثناء حذف البيانات', 'danger');
                }
            } else {
                app.showAlert('تم إلغاء العملية - النص المدخل غير صحيح', 'info');
            }
        },
        'تأكيد الحذف النهائي'
    );
}

// إعادة تهيئة النظام بالبيانات الافتراضية
async function resetToDefaults() {
    app.showConfirm(
        'هل تريد إعادة تهيئة النظام بالبيانات الافتراضية؟\n\nسيتم إنشاء منتجات وعملاء نموذجيين.',
        async () => {
            try {
                const success = await db.resetToDefaults();
                if (success) {
                    app.showAlert('تم إعادة تهيئة النظام بالبيانات الافتراضية بنجاح', 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                } else {
                    app.showAlert('حدث خطأ أثناء إعادة تهيئة النظام', 'danger');
                }
            } catch (error) {
                console.error('خطأ في إعادة تهيئة النظام:', error);
                app.showAlert('حدث خطأ أثناء إعادة تهيئة النظام', 'danger');
            }
        },
        'تأكيد إعادة التهيئة'
    );
}

// دوال wrapper للتعامل مع onclick
function handleClearAllData() {
    clearAllData().catch(error => {
        console.error('خطأ في حذف البيانات:', error);
        app.showAlert('حدث خطأ أثناء حذف البيانات', 'danger');
    });
}

function handleResetToDefaults() {
    resetToDefaults().catch(error => {
        console.error('خطأ في إعادة التهيئة:', error);
        app.showAlert('حدث خطأ أثناء إعادة التهيئة', 'danger');
    });
}

// حساب حجم البيانات
function calculateDataSize() {
    try {
        const data = db.exportData();
        const sizeInBytes = new Blob([data]).size;
        const sizeInKB = (sizeInBytes / 1024).toFixed(2);
        return `${db.toArabicNumbers(sizeInKB)} كيلوبايت`;
    } catch (error) {
        return 'غير محدد';
    }
}

// تحديث معلومات آخر نسخة احتياطية
function updateLastBackupInfo() {
    const lastBackup = localStorage.getItem('lastBackup');
    const lastBackupElement = document.getElementById('lastBackup');
    
    if (lastBackupElement) {
        if (lastBackup) {
            lastBackupElement.textContent = db.formatDateTime(lastBackup);
        } else {
            lastBackupElement.textContent = 'لم يتم إنشاء نسخة احتياطية';
        }
    }
}
