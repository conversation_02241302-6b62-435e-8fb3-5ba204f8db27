// نظام المبيعات
let currentSale = {
    items: [],
    customerId: 'guest',
    paymentMethod: 'cash',
    discount: 0,
    notes: ''
};

async function loadSales() {
    const pageContent = document.getElementById('pageContent');

    // إنشاء تخطيط المبيعات
    const salesLayout = document.createElement('div');
    salesLayout.style.display = 'grid';
    salesLayout.style.gridTemplateColumns = '1fr 400px';
    salesLayout.style.gap = '1.5rem';
    salesLayout.style.height = 'calc(100vh - 150px)';

    // الجانب الأيسر - المنتجات
    const productsSection = createProductsSection();
    salesLayout.appendChild(productsSection);

    // الجانب الأيمن - سلة المشتريات
    const cartSection = await createCartSection();
    salesLayout.appendChild(cartSection);

    pageContent.appendChild(salesLayout);

    // تحميل المنتجات
    await loadProductsForSale();
}

// إنشاء قسم المنتجات
function createProductsSection() {
    const section = document.createElement('div');
    section.className = 'card';
    section.style.height = '100%';
    section.style.overflow = 'hidden';
    section.style.display = 'flex';
    section.style.flexDirection = 'column';
    
    // رأس القسم
    const header = document.createElement('div');
    header.className = 'card-header';
    header.innerHTML = `
        <h3 class="card-title">المنتجات</h3>
        <div style="display: flex; gap: 0.5rem;">
            <input type="text" id="productSearchSales" placeholder="البحث في المنتجات..." 
                   style="padding: 0.5rem; border: 1px solid #ddd; border-radius: 8px; width: 250px;">
            <select id="categoryFilterSales" style="padding: 0.5rem; border: 1px solid #ddd; border-radius: 8px;">
                <option value="">جميع الفئات</option>
            </select>
        </div>
    `;
    
    section.appendChild(header);
    
    // محتوى المنتجات
    const content = document.createElement('div');
    content.style.flex = '1';
    content.style.overflow = 'auto';
    content.style.padding = '1rem';
    content.id = 'productsGrid';
    
    section.appendChild(content);
    
    // إضافة مستمعي الأحداث للبحث والفلترة
    setTimeout(() => {
        document.getElementById('productSearchSales').addEventListener('input', filterProductsForSale);
        document.getElementById('categoryFilterSales').addEventListener('change', filterProductsForSale);
    }, 100);
    
    return section;
}

// إنشاء قسم سلة المشتريات
async function createCartSection() {
    const section = document.createElement('div');
    section.style.display = 'flex';
    section.style.flexDirection = 'column';
    section.style.gap = '1rem';
    section.style.height = '100%';

    // معلومات العميل
    const customerCard = await createCustomerCard();
    section.appendChild(customerCard);
    
    // سلة المشتريات
    const cartCard = createCartCard();
    section.appendChild(cartCard);
    
    // ملخص الفاتورة
    const summaryCard = createSummaryCard();
    section.appendChild(summaryCard);
    
    return section;
}

// إنشاء بطاقة العميل
async function createCustomerCard() {
    const card = document.createElement('div');
    card.className = 'card';

    const customers = await db.getCustomers();
    
    card.innerHTML = `
        <div class="card-header">
            <h4 class="card-title">العميل</h4>
        </div>
        <div style="padding: 1rem;">
            <select id="customerSelect" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 8px;">
                ${customers.map(customer => 
                    `<option value="${customer.id}" ${customer.id === 'guest' ? 'selected' : ''}>${customer.name}</option>`
                ).join('')}
            </select>
        </div>
    `;
    
    // إضافة مستمع الحدث
    setTimeout(() => {
        document.getElementById('customerSelect').addEventListener('change', (e) => {
            currentSale.customerId = e.target.value;
        });
    }, 100);
    
    return card;
}

// إنشاء بطاقة سلة المشتريات
function createCartCard() {
    const card = document.createElement('div');
    card.className = 'card';
    card.style.flex = '1';
    card.style.overflow = 'hidden';
    card.style.display = 'flex';
    card.style.flexDirection = 'column';
    
    card.innerHTML = `
        <div class="card-header">
            <h4 class="card-title">سلة المشتريات</h4>
            <button class="btn btn-danger" onclick="clearCart()">
                <i class="fas fa-trash"></i> مسح الكل
            </button>
        </div>
        <div id="cartItems" style="flex: 1; overflow: auto; padding: 1rem;">
            <div class="empty-cart" style="text-align: center; color: #666; padding: 2rem;">
                <i class="fas fa-shopping-cart" style="font-size: 3rem; margin-bottom: 1rem; color: #ddd;"></i>
                <p>السلة فارغة</p>
                <p>اختر المنتجات لإضافتها</p>
            </div>
        </div>
    `;
    
    return card;
}

// إنشاء بطاقة الملخص
function createSummaryCard() {
    const card = document.createElement('div');
    card.className = 'card';
    
    card.innerHTML = `
        <div class="card-header">
            <h4 class="card-title">ملخص الفاتورة</h4>
        </div>
        <div style="padding: 1rem;">
            <div class="summary-row">
                <span>المجموع الفرعي:</span>
                <span id="subtotal">٠.٠٠ ر.ع</span>
            </div>
            <div class="summary-row">
                <span>الخصم:</span>
                <input type="number" id="discountInput" value="0" min="0" 
                       style="width: 80px; padding: 0.25rem; border: 1px solid #ddd; border-radius: 4px;">
                <span>ر.ع</span>
            </div>
            <div class="summary-row">
                <span id="taxLabel">الضريبة (٥%):</span>
                <span id="taxAmount">٠.٠٠ ر.ع</span>
            </div>
            <div class="summary-row total-row">
                <span><strong>المجموع الكلي:</strong></span>
                <span id="totalAmount"><strong>٠.٠٠ ر.ع</strong></span>
            </div>
            
            <div style="margin: 1rem 0;">
                <label>طريقة الدفع:</label>
                <select id="paymentMethod" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 8px; margin-top: 0.5rem;">
                    <option value="cash">نقداً</option>
                    <option value="credit">آجل</option>
                </select>
            </div>
            
            <div style="margin: 1rem 0;">
                <label>ملاحظات:</label>
                <textarea id="saleNotes" placeholder="ملاحظات إضافية..." 
                         style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 8px; margin-top: 0.5rem; resize: vertical;" 
                         rows="2"></textarea>
            </div>
            
            <button id="completeSaleBtn" class="btn btn-success" style="width: 100%; padding: 1rem; font-size: 1.1rem;" onclick="completeSale()">
                <i class="fas fa-check"></i> إتمام البيع
            </button>
        </div>
        
        <style>
            .summary-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.5rem 0;
                border-bottom: 1px solid #eee;
            }
            .total-row {
                border-top: 2px solid #333;
                border-bottom: none;
                font-size: 1.1rem;
                margin-top: 0.5rem;
            }
        </style>
    `;
    
    // إضافة مستمعي الأحداث
    setTimeout(() => {
        document.getElementById('discountInput').addEventListener('input', updateSummary);
        document.getElementById('paymentMethod').addEventListener('change', (e) => {
            currentSale.paymentMethod = e.target.value;
        });
        document.getElementById('saleNotes').addEventListener('input', (e) => {
            currentSale.notes = e.target.value;
        });
    }, 100);
    
    return card;
}

// تحميل المنتجات للبيع
async function loadProductsForSale() {
    const products = (await db.getProducts()).filter(p => p.quantity > 0);
    const grid = document.getElementById('productsGrid');
    const categoryFilter = document.getElementById('categoryFilterSales');
    
    // تحديث فلتر الفئات
    const categories = [...new Set(products.map(p => p.category).filter(c => c))];
    categoryFilter.innerHTML = `
        <option value="">جميع الفئات</option>
        ${categories.map(cat => `<option value="${cat}">${cat}</option>`).join('')}
    `;
    
    if (products.length === 0) {
        grid.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-box-open"></i>
                <h3>لا توجد منتجات متاحة</h3>
                <p>تأكد من إضافة منتجات وتوفر كميات كافية</p>
            </div>
        `;
        return;
    }
    
    // إنشاء شبكة المنتجات
    grid.innerHTML = `
        <div class="products-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 1rem;">
            ${products.map(product => createProductCard(product)).join('')}
        </div>
    `;
}

// إنشاء بطاقة منتج
function createProductCard(product) {
    return `
        <div class="product-card" data-product-id="${product.id}" onclick="addToCart('${product.id}')" 
             style="border: 1px solid #ddd; border-radius: 8px; padding: 1rem; cursor: pointer; transition: all 0.3s ease; background: white;">
            <div style="text-align: center; margin-bottom: 0.5rem;">
                ${product.image ? 
                    `<img src="${product.image}" alt="${product.name}" style="width: 60px; height: 60px; object-fit: cover; border-radius: 8px;">` :
                    `<div style="width: 60px; height: 60px; background: #f0f0f0; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin: 0 auto;"><i class="fas fa-box" style="color: #ccc;"></i></div>`
                }
            </div>
            <h5 style="margin: 0.5rem 0; font-size: 0.9rem; text-align: center;">${product.name}</h5>
            <div style="text-align: center; color: #666; font-size: 0.8rem; margin-bottom: 0.5rem;">
                الكمية: ${db.toArabicNumbers(product.quantity)}
            </div>
            <div style="text-align: center; font-weight: bold; color: var(--primary-color);">
                ${db.formatCurrency(product.price)}
            </div>
        </div>
    `;
}

// فلترة المنتجات للبيع
async function filterProductsForSale() {
    const searchTerm = document.getElementById('productSearchSales').value.toLowerCase();
    const selectedCategory = document.getElementById('categoryFilterSales').value;
    const productCards = document.querySelectorAll('.product-card');

    for (const card of productCards) {
        const productId = card.dataset.productId;
        const product = await db.getProductById(productId);
        
        const matchesSearch = product.name.toLowerCase().includes(searchTerm);
        const matchesCategory = !selectedCategory || product.category === selectedCategory;

        card.style.display = matchesSearch && matchesCategory ? 'block' : 'none';
    }
}

// إضافة منتج إلى السلة
async function addToCart(productId) {
    const product = await db.getProductById(productId);
    if (!product || product.quantity <= 0) {
        app.showAlert('المنتج غير متاح', 'warning');
        return;
    }
    
    // البحث عن المنتج في السلة
    const existingItem = currentSale.items.find(item => item.productId === productId);
    
    if (existingItem) {
        if (existingItem.quantity < product.quantity) {
            existingItem.quantity++;
        } else {
            app.showAlert('لا توجد كمية كافية من هذا المنتج', 'warning');
            return;
        }
    } else {
        currentSale.items.push({
            productId: productId,
            productName: product.name,
            price: product.price,
            quantity: 1
        });
    }
    
    updateCartDisplay();
    await updateSummary();
}

// تحديث عرض السلة
function updateCartDisplay() {
    const cartItems = document.getElementById('cartItems');
    
    if (currentSale.items.length === 0) {
        cartItems.innerHTML = `
            <div class="empty-cart" style="text-align: center; color: #666; padding: 2rem;">
                <i class="fas fa-shopping-cart" style="font-size: 3rem; margin-bottom: 1rem; color: #ddd;"></i>
                <p>السلة فارغة</p>
                <p>اختر المنتجات لإضافتها</p>
            </div>
        `;
        return;
    }
    
    cartItems.innerHTML = currentSale.items.map((item, index) => `
        <div class="cart-item" style="border: 1px solid #eee; border-radius: 8px; padding: 0.75rem; margin-bottom: 0.5rem;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                <strong style="font-size: 0.9rem;">${item.productName}</strong>
                <button onclick="removeFromCart(${index})" style="background: none; border: none; color: #dc3545; cursor: pointer;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <button onclick="decreaseQuantity(${index})" style="background: #f8f9fa; border: 1px solid #ddd; border-radius: 4px; width: 25px; height: 25px; display: flex; align-items: center; justify-content: center; cursor: pointer;">-</button>
                    <span style="min-width: 30px; text-align: center;">${db.toArabicNumbers(item.quantity)}</span>
                    <button onclick="increaseQuantity(${index})" style="background: #f8f9fa; border: 1px solid #ddd; border-radius: 4px; width: 25px; height: 25px; display: flex; align-items: center; justify-content: center; cursor: pointer;">+</button>
                </div>
                <div style="text-align: left;">
                    <div style="font-size: 0.8rem; color: #666;">${db.formatCurrency(item.price)} × ${db.toArabicNumbers(item.quantity)}</div>
                    <div style="font-weight: bold;">${db.formatCurrency(item.price * item.quantity)}</div>
                </div>
            </div>
        </div>
    `).join('');
}

// زيادة الكمية
async function increaseQuantity(index) {
    const item = currentSale.items[index];
    const product = await db.getProductById(item.productId);
    
    if (item.quantity < product.quantity) {
        item.quantity++;
        updateCartDisplay();
        await updateSummary();
    } else {
        app.showAlert('لا توجد كمية كافية من هذا المنتج', 'warning');
    }
}

// تقليل الكمية
async function decreaseQuantity(index) {
    const item = currentSale.items[index];
    
    if (item.quantity > 1) {
        item.quantity--;
        updateCartDisplay();
        await updateSummary();
    } else {
        await removeFromCart(index);
    }
}

// إزالة من السلة
async function removeFromCart(index) {
    currentSale.items.splice(index, 1);
    updateCartDisplay();
    await updateSummary();
}

// مسح السلة
function clearCart() {
    if (currentSale.items.length === 0) return;

    app.showConfirm('هل أنت متأكد من مسح جميع العناصر؟', async () => {
        currentSale.items = [];
        updateCartDisplay();
        await updateSummary();
    });
}

// تحديث الملخص
async function updateSummary() {
    const subtotal = currentSale.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const discount = parseFloat(document.getElementById('discountInput')?.value || 0);
    const settings = await db.getSettings();
    const taxAmount = (subtotal - discount) * (settings.taxRate / 100);
    const total = subtotal - discount + taxAmount;
    
    currentSale.discount = discount;
    
    if (document.getElementById('subtotal')) {
        document.getElementById('subtotal').textContent = db.formatCurrencySync(subtotal, settings?.currency);
        document.getElementById('taxAmount').textContent = db.formatCurrencySync(taxAmount, settings?.currency);
        document.getElementById('totalAmount').textContent = db.formatCurrencySync(total, settings?.currency);
        document.getElementById('taxLabel').textContent = `الضريبة (${db.toArabicNumbers(settings?.taxRate || 5)}%):`;
    }
    
    // تفعيل/تعطيل زر الإتمام
    const completeBtn = document.getElementById('completeSaleBtn');
    if (completeBtn) {
        completeBtn.disabled = currentSale.items.length === 0;
        completeBtn.style.opacity = currentSale.items.length === 0 ? '0.5' : '1';
    }
}

// إتمام البيع
async function completeSale() {
    if (currentSale.items.length === 0) {
        app.showAlert('السلة فارغة', 'warning');
        return;
    }

    const subtotal = currentSale.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const discount = currentSale.discount;
    const settings = await db.getSettings();
    const taxAmount = (subtotal - discount) * (settings.taxRate / 100);
    const total = subtotal - discount + taxAmount;
    
    const saleData = {
        customerId: currentSale.customerId,
        items: currentSale.items,
        subtotal: subtotal,
        discount: discount,
        taxAmount: taxAmount,
        total: total,
        paymentMethod: currentSale.paymentMethod,
        notes: currentSale.notes,
        invoiceNumber: generateInvoiceNumber()
    };
    
    try {
        const savedSale = await db.saveSale(saleData);
        
        app.showAlert('تم إتمام البيع بنجاح', 'success');
        
        // طباعة الفاتورة
        await printInvoice(savedSale);
        
        // إعادة تعيين السلة
        currentSale = {
            items: [],
            customerId: 'guest',
            paymentMethod: 'cash',
            discount: 0,
            notes: ''
        };
        
        // إعادة تحميل الصفحة
        loadSales();
        
    } catch (error) {
        app.showAlert('حدث خطأ أثناء إتمام البيع', 'danger');
    }
}

// إنشاء رقم فاتورة
function generateInvoiceNumber() {
    const now = new Date();
    const year = now.getFullYear().toString().slice(-2);
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const time = now.getTime().toString().slice(-4);
    
    return `${year}${month}${day}${time}`;
}

// طباعة الفاتورة
async function printInvoice(sale) {
    const customer = await db.getCustomerById(sale.customerId);
    const settings = await db.getSettings();
    
    const content = `
        <div style="text-align: center; margin-bottom: 30px;">
            <h1>${settings.companyName}</h1>
            <p>${settings.companyAddress}</p>
            <p>هاتف: ${settings.companyPhone} | بريد: ${settings.companyEmail}</p>
        </div>
        
        <div style="display: flex; justify-content: space-between; margin-bottom: 20px;">
            <div>
                <strong>رقم الفاتورة:</strong> ${db.toArabicNumbers(sale.invoiceNumber)}<br>
                <strong>التاريخ:</strong> ${db.formatDateTime(sale.createdAt)}
            </div>
            <div>
                <strong>العميل:</strong> ${customer ? customer.name : 'ضيف'}<br>
                <strong>طريقة الدفع:</strong> ${sale.paymentMethod === 'cash' ? 'نقداً' : 'آجل'}
            </div>
        </div>
        
        <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
            <thead>
                <tr style="background-color: #f5f5f5;">
                    <th style="border: 1px solid #ddd; padding: 8px;">المنتج</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">السعر</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">الكمية</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">المجموع</th>
                </tr>
            </thead>
            <tbody>
                ${sale.items.map(item => `
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px;">${item.productName}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${db.formatCurrency(item.price)}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${db.toArabicNumbers(item.quantity)}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${db.formatCurrency(item.price * item.quantity)}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
        
        <div style="text-align: left; margin-top: 20px;">
            <p><strong>المجموع الفرعي:</strong> ${db.formatCurrency(sale.subtotal)}</p>
            ${sale.discount > 0 ? `<p><strong>الخصم:</strong> ${db.formatCurrency(sale.discount)}</p>` : ''}
            <p><strong>الضريبة (${settings.taxRate}%):</strong> ${db.formatCurrency(sale.taxAmount)}</p>
            <p style="font-size: 1.2em; border-top: 2px solid #333; padding-top: 10px;"><strong>المجموع الكلي:</strong> ${db.formatCurrency(sale.total)}</p>
        </div>
        
        ${sale.notes ? `<div style="margin-top: 20px;"><strong>ملاحظات:</strong> ${sale.notes}</div>` : ''}
        
        <div style="text-align: center; margin-top: 30px; font-size: 0.9em;">
            <p>شكراً لتعاملكم معنا</p>
        </div>
    `;
    
    app.printContent(content, `فاتورة رقم ${sale.invoiceNumber}`);
}
