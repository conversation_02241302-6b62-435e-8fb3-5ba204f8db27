@echo off
chcp 65001 >nul
echo ========================================
echo    بناء تطبيق BakryAfand POS
echo ========================================
echo.

:: التحقق من وجود Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: Node.js غير مثبت على النظام
    echo يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

:: التحقق من وجود npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: npm غير متاح
    pause
    exit /b 1
)

echo ✅ Node.js و npm متاحان
echo.

:: التحقق من وجود package.json
if not exist "package.json" (
    echo ❌ خطأ: ملف package.json غير موجود
    pause
    exit /b 1
)

echo 📦 تثبيت المتطلبات...
call npm install
if %errorlevel% neq 0 (
    echo ❌ خطأ في تثبيت المتطلبات
    pause
    exit /b 1
)

echo ✅ تم تثبيت المتطلبات بنجاح
echo.

:: إنشاء مجلد assets إذا لم يكن موجوداً
if not exist "assets" (
    echo 📁 إنشاء مجلد assets...
    mkdir assets
)

:: إنشاء أيقونة بسيطة إذا لم تكن موجودة
if not exist "assets\icon.png" (
    echo 🎨 إنشاء أيقونة افتراضية...
    echo. > assets\icon.png
)

echo 🔨 بناء التطبيق للويندوز...
call npm run build-win
if %errorlevel% neq 0 (
    echo ❌ خطأ في بناء التطبيق
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ تم بناء التطبيق بنجاح!
echo ========================================
echo.
echo 📁 ملفات الإخراج في مجلد: dist\
echo.

:: عرض الملفات المبنية
if exist "dist" (
    echo 📋 الملفات المبنية:
    dir /b dist\*.exe 2>nul
    dir /b dist\*.msi 2>nul
    echo.
)

echo 🚀 يمكنك الآن تشغيل التطبيق أو توزيع ملف التثبيت
echo.

:: سؤال عن فتح مجلد الإخراج
set /p open_folder="هل تريد فتح مجلد الإخراج؟ (y/n): "
if /i "%open_folder%"=="y" (
    if exist "dist" (
        explorer dist
    )
)

pause
